﻿<#@ template debug="false" hostspecific="false" language="C#" #>
<#@ include file="EF.Utility.CS.ttinclude"#>
<#@ output extension=".cs" #>
<#
string   modelNamespace="Pos";
CodeGenerationTools code =new CodeGenerationTools(this);
MetadataLoader Loader =new MetadataLoader(this);
CodeRegion region=new CodeRegion(this,1);
MetadataTools ef=new MetadataTools(this);
string inputFile=modelNamespace+@".Model\DbContext\\FoodDB.edmx";
EdmItemCollection Itemcollection = Loader.CreateEdmItemCollection(inputFile);
string namespaceName=code.VsNamespaceSuggestion();
EntityFrameworkTemplateFileManager fileManager = EntityFrameworkTemplateFileManager.Create(this);
#>

using <#=modelNamespace#>.Domain.IRepository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace <#=modelNamespace#>.RepositoryFactory
{
 public partial class RepositorySession {
<#
foreach(EntityType entity in Itemcollection.GetItems<EntityType>().OrderBy(e=>e.Name))
{
#>

  I<#=entity.Name#>Repository _<#=entity.Name#>;
    public I<#=entity.Name#>Repository <#=entity.Name#>
        {
            get
            {
                if (_<#=entity.Name#> == null) _<#=entity.Name#> = AbstractRepositoryFactory.Create_<#=entity.Name#>();
                return _<#=entity.Name#>;
            }
        }
        
 

  

<#}#>
 }
}

