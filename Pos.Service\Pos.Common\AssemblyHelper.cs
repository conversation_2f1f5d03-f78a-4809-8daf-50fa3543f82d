﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;

namespace Pos.Common
{
    public class AssemblyHelper
    {
        /// <summary>
        /// 公共反射创建实体类方法
        /// </summary>
        /// <param name="assemblyName">程序集</param>
        /// <param name="nameSpace">命名空间</param>
        /// <param name="className">类名称</param>
        /// <returns></returns>
        public static T CreateInstance<T>(string assemblyName, string nameSpace, string className)
        {
            try
            {
                string fullName = nameSpace + "." + className;//命名空间.类型名
                object ect = Assembly.Load(assemblyName).CreateInstance(fullName, false, BindingFlags.Default, null, null, null, null);//加载程序集，创建程序集里面的 命名空间.类型名 实例
                if (ect == null)
                    throw new Exception("类型初始化失败！");

                return (T)ect;//类型转换并返回
            }
            catch (Exception ex) { Console.WriteLine(ex.Message + "===" + ex.StackTrace); return default(T); }
        }
    }
}
