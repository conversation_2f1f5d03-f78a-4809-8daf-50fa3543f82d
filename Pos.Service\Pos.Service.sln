﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 2012
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Pos.Service", "Pos.Service\Pos.Service.csproj", "{03ED58D1-002A-47FF-9415-349D7FF3737E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Pos.RepositoryFactory", "Pos.RepositoryFactory\Pos.RepositoryFactory.csproj", "{DD0C11D5-57BD-4148-8175-656FAD45DC5B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Pos.Repository", "Pos.Repository\Pos.Repository.csproj", "{A62D073E-370A-48E0-867C-3416BE196B23}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Pos.Model", "Pos.Model\Pos.Model.csproj", "{0752C409-20CA-41B4-B9B3-BEBC6D1602BD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Pos.Domain", "Pos.Domain\Pos.Domain.csproj", "{48B91AC4-ED2F-48C4-84FF-263CFE5AB65A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Pos.Common", "Pos.Common\Pos.Common.csproj", "{B7127FAF-ED89-4210-8ADC-EC29099CE7FD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Pos.Application", "Pos.Application\Pos.Application.csproj", "{1EC5741D-6CD4-401F-A868-E4D6DF406142}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Pos.Drive", "Pos.Drive\Pos.Drive.csproj", "{E3E2EF69-7DC1-461B-89EF-113E78DC84BD}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "02 驱动层", "02 驱动层", "{CCB5FE9D-1CFD-4B05-B8F6-BE674036BC54}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "01 服务层", "01 服务层", "{FEA17D1A-B88C-4D33-92D9-BA75A1EBC468}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "03 逻辑层", "03 逻辑层", "{2EF21521-F665-42A7-984C-61A3C0143B63}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "04 数据库访问层", "04 数据库访问层", "{599DEFEB-1574-4214-9154-0B06D04A0FCC}"
EndProject
Global
	GlobalSection(TeamFoundationVersionControl) = preSolution
		SccNumberOfProjects = 9
		SccEnterpriseProvider = {4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}
		SccTeamFoundationServer = http://192.168.2.11:8080/tfs/serviceapplication
		SccLocalPath0 = .
		SccProjectUniqueName1 = Pos.Application\\Pos.Application.csproj
		SccProjectTopLevelParentUniqueName1 = Pos.Service.sln
		SccProjectName1 = Pos.Application
		SccLocalPath1 = Pos.Application
		SccProjectUniqueName2 = Pos.Common\\Pos.Common.csproj
		SccProjectName2 = Pos.Common
		SccLocalPath2 = Pos.Common
		SccProjectUniqueName3 = Pos.Domain\\Pos.Domain.csproj
		SccProjectName3 = Pos.Domain
		SccLocalPath3 = Pos.Domain
		SccProjectUniqueName4 = Pos.Drive\\Pos.Drive.csproj
		SccProjectTopLevelParentUniqueName4 = Pos.Service.sln
		SccProjectName4 = Pos.Drive
		SccLocalPath4 = Pos.Drive
		SccProjectUniqueName5 = Pos.Model\\Pos.Model.csproj
		SccProjectName5 = Pos.Model
		SccLocalPath5 = Pos.Model
		SccProjectUniqueName6 = Pos.Repository\\Pos.Repository.csproj
		SccProjectTopLevelParentUniqueName6 = Pos.Service.sln
		SccProjectName6 = Pos.Repository
		SccLocalPath6 = Pos.Repository
		SccProjectUniqueName7 = Pos.RepositoryFactory\\Pos.RepositoryFactory.csproj
		SccProjectTopLevelParentUniqueName7 = Pos.Service.sln
		SccProjectName7 = Pos.RepositoryFactory
		SccLocalPath7 = Pos.RepositoryFactory
		SccProjectUniqueName8 = Pos.Service\\Pos.Service.csproj
		SccProjectTopLevelParentUniqueName8 = Pos.Service.sln
		SccProjectName8 = Pos.Service
		SccLocalPath8 = Pos.Service
	EndGlobalSection
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{03ED58D1-002A-47FF-9415-349D7FF3737E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{03ED58D1-002A-47FF-9415-349D7FF3737E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{03ED58D1-002A-47FF-9415-349D7FF3737E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{03ED58D1-002A-47FF-9415-349D7FF3737E}.Release|Any CPU.Build.0 = Release|Any CPU
		{DD0C11D5-57BD-4148-8175-656FAD45DC5B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DD0C11D5-57BD-4148-8175-656FAD45DC5B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DD0C11D5-57BD-4148-8175-656FAD45DC5B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DD0C11D5-57BD-4148-8175-656FAD45DC5B}.Release|Any CPU.Build.0 = Release|Any CPU
		{A62D073E-370A-48E0-867C-3416BE196B23}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A62D073E-370A-48E0-867C-3416BE196B23}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A62D073E-370A-48E0-867C-3416BE196B23}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A62D073E-370A-48E0-867C-3416BE196B23}.Release|Any CPU.Build.0 = Release|Any CPU
		{0752C409-20CA-41B4-B9B3-BEBC6D1602BD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0752C409-20CA-41B4-B9B3-BEBC6D1602BD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0752C409-20CA-41B4-B9B3-BEBC6D1602BD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0752C409-20CA-41B4-B9B3-BEBC6D1602BD}.Release|Any CPU.Build.0 = Release|Any CPU
		{48B91AC4-ED2F-48C4-84FF-263CFE5AB65A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{48B91AC4-ED2F-48C4-84FF-263CFE5AB65A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{48B91AC4-ED2F-48C4-84FF-263CFE5AB65A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{48B91AC4-ED2F-48C4-84FF-263CFE5AB65A}.Release|Any CPU.Build.0 = Release|Any CPU
		{B7127FAF-ED89-4210-8ADC-EC29099CE7FD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B7127FAF-ED89-4210-8ADC-EC29099CE7FD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B7127FAF-ED89-4210-8ADC-EC29099CE7FD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B7127FAF-ED89-4210-8ADC-EC29099CE7FD}.Release|Any CPU.Build.0 = Release|Any CPU
		{1EC5741D-6CD4-401F-A868-E4D6DF406142}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1EC5741D-6CD4-401F-A868-E4D6DF406142}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1EC5741D-6CD4-401F-A868-E4D6DF406142}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1EC5741D-6CD4-401F-A868-E4D6DF406142}.Release|Any CPU.Build.0 = Release|Any CPU
		{E3E2EF69-7DC1-461B-89EF-113E78DC84BD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E3E2EF69-7DC1-461B-89EF-113E78DC84BD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E3E2EF69-7DC1-461B-89EF-113E78DC84BD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E3E2EF69-7DC1-461B-89EF-113E78DC84BD}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{03ED58D1-002A-47FF-9415-349D7FF3737E} = {FEA17D1A-B88C-4D33-92D9-BA75A1EBC468}
		{A62D073E-370A-48E0-867C-3416BE196B23} = {599DEFEB-1574-4214-9154-0B06D04A0FCC}
		{DD0C11D5-57BD-4148-8175-656FAD45DC5B} = {599DEFEB-1574-4214-9154-0B06D04A0FCC}
		{1EC5741D-6CD4-401F-A868-E4D6DF406142} = {2EF21521-F665-42A7-984C-61A3C0143B63}
		{E3E2EF69-7DC1-461B-89EF-113E78DC84BD} = {CCB5FE9D-1CFD-4B05-B8F6-BE674036BC54}
	EndGlobalSection
EndGlobal
