﻿
using ComponentApplicationServiceInterface.Web;
using Pos.Model.DbContext;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Pos.Application
{
 public partial class AddItemApp : AppBase<Pos.Model.DbContext.AddItem> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.AddItem> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.AddItem;
        }
   
        
 
 }
  

 public partial class AiTypeApp : AppBase<Pos.Model.DbContext.AiType> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.AiType> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.AiType;
        }
   
        
 
 }
  

 public partial class AmountLogApp : AppBase<Pos.Model.DbContext.AmountLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.AmountLog> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.AmountLog;
        }
   
        
 
 }
  

 public partial class CarLeaveLogApp : AppBase<Pos.Model.DbContext.CarLeaveLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.CarLeaveLog> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.CarLeaveLog;
        }
   
        
 
 }
  

 public partial class CashierApp : AppBase<Pos.Model.DbContext.Cashier> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.Cashier> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.Cashier;
        }
   
        
 
 }
  

 public partial class ClearDataLogApp : AppBase<Pos.Model.DbContext.ClearDataLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.ClearDataLog> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.ClearDataLog;
        }
   
        
 
 }
  

 public partial class DeadLockLogApp : AppBase<Pos.Model.DbContext.DeadLockLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.DeadLockLog> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.DeadLockLog;
        }
   
        
 
 }
  

 public partial class DepositInfoApp : AppBase<Pos.Model.DbContext.DepositInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.DepositInfo> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.DepositInfo;
        }
   
        
 
 }
  

 public partial class DeptApp : AppBase<Pos.Model.DbContext.Dept> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.Dept> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.Dept;
        }
   
        
 
 }
  

 public partial class DeptBanSetApp : AppBase<Pos.Model.DbContext.DeptBanSet> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.DeptBanSet> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.DeptBanSet;
        }
   
        
 
 }
  

 public partial class dtpropertiesApp : AppBase<Pos.Model.DbContext.dtproperties> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.dtproperties> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.dtproperties;
        }
   
        
 
 }
  

 public partial class FdCashApp : AppBase<Pos.Model.DbContext.FdCash> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FdCash> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FdCash;
        }
   
        
 
 }
  

 public partial class FdCash_TrackApp : AppBase<Pos.Model.DbContext.FdCash_Track> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FdCash_Track> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FdCash_Track;
        }
   
        
 
 }
  

 public partial class FdCashBakApp : AppBase<Pos.Model.DbContext.FdCashBak> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FdCashBak> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FdCashBak;
        }
   
        
 
 }
  

 public partial class FdCashBak_BApp : AppBase<Pos.Model.DbContext.FdCashBak_B> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FdCashBak_B> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FdCashBak_B;
        }
   
        
 
 }
  

 public partial class FdCashBak_BakApp : AppBase<Pos.Model.DbContext.FdCashBak_Bak> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FdCashBak_Bak> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FdCashBak_Bak;
        }
   
        
 
 }
  

 public partial class FdDetTypeApp : AppBase<Pos.Model.DbContext.FdDetType> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FdDetType> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FdDetType;
        }
   
        
 
 }
  

 public partial class FdImageApp : AppBase<Pos.Model.DbContext.FdImage> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FdImage> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FdImage;
        }
   
        
 
 }
  

 public partial class FdInvApp : AppBase<Pos.Model.DbContext.FdInv> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FdInv> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FdInv;
        }
   
        
 
 }
  

 public partial class FdInv_BApp : AppBase<Pos.Model.DbContext.FdInv_B> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FdInv_B> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FdInv_B;
        }
   
        
 
 }
  

 public partial class FdInv_BakApp : AppBase<Pos.Model.DbContext.FdInv_Bak> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FdInv_Bak> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FdInv_Bak;
        }
   
        
 
 }
  

 public partial class FdInv_ExchangeLogApp : AppBase<Pos.Model.DbContext.FdInv_ExchangeLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FdInv_ExchangeLog> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FdInv_ExchangeLog;
        }
   
        
 
 }
  

 public partial class FdInvCashItemApp : AppBase<Pos.Model.DbContext.FdInvCashItem> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FdInvCashItem> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FdInvCashItem;
        }
   
        
 
 }
  

 public partial class FdInvDescApp : AppBase<Pos.Model.DbContext.FdInvDesc> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FdInvDesc> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FdInvDesc;
        }
   
        
 
 }
  

 public partial class FdTicketApp : AppBase<Pos.Model.DbContext.FdTicket> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FdTicket> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FdTicket;
        }
   
        
 
 }
  

 public partial class FdTimePriceApp : AppBase<Pos.Model.DbContext.FdTimePrice> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FdTimePrice> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FdTimePrice;
        }
   
        
 
 }
  

 public partial class FdTimeZoneApp : AppBase<Pos.Model.DbContext.FdTimeZone> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FdTimeZone> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FdTimeZone;
        }
   
        
 
 }
  

 public partial class FdTypeApp : AppBase<Pos.Model.DbContext.FdType> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FdType> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FdType;
        }
   
        
 
 }
  

 public partial class FdUserApp : AppBase<Pos.Model.DbContext.FdUser> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FdUser> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FdUser;
        }
   
        
 
 }
  

 public partial class FdUserGradeApp : AppBase<Pos.Model.DbContext.FdUserGrade> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FdUserGrade> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FdUserGrade;
        }
   
        
 
 }
  

 public partial class FdUserRightsApp : AppBase<Pos.Model.DbContext.FdUserRights> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FdUserRights> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FdUserRights;
        }
   
        
 
 }
  

 public partial class FestivalTimeApp : AppBase<Pos.Model.DbContext.FestivalTime> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FestivalTime> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FestivalTime;
        }
   
        
 
 }
  

 public partial class FoodApp : AppBase<Pos.Model.DbContext.Food> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.Food> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.Food;
        }
   
        
 
 }
  

 public partial class FoodCalApp : AppBase<Pos.Model.DbContext.FoodCal> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FoodCal> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FoodCal;
        }
   
        
 
 }
  

 public partial class FoodLabelApp : AppBase<Pos.Model.DbContext.FoodLabel> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FoodLabel> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FoodLabel;
        }
   
        
 
 }
  

 public partial class FoodOrderMApp : AppBase<Pos.Model.DbContext.FoodOrderM> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FoodOrderM> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FoodOrderM;
        }
   
        
 
 }
  

 public partial class FPrnApp : AppBase<Pos.Model.DbContext.FPrn> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FPrn> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FPrn;
        }
   
        
 
 }
  

 public partial class FPrnDataApp : AppBase<Pos.Model.DbContext.FPrnData> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FPrnData> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FPrnData;
        }
   
        
 
 }
  

 public partial class FPrnData_BakApp : AppBase<Pos.Model.DbContext.FPrnData_Bak> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FPrnData_Bak> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FPrnData_Bak;
        }
   
        
 
 }
  

 public partial class FreePackageCoupon_RecordApp : AppBase<Pos.Model.DbContext.FreePackageCoupon_Record> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FreePackageCoupon_Record> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FreePackageCoupon_Record;
        }
   
        
 
 }
  

 public partial class FtInfoApp : AppBase<Pos.Model.DbContext.FtInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.FtInfo> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.FtInfo;
        }
   
        
 
 }
  

 public partial class GDDB20InfoApp : AppBase<Pos.Model.DbContext.GDDB20Info> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.GDDB20Info> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.GDDB20Info;
        }
   
        
 
 }
  

 public partial class GDDBInfoApp : AppBase<Pos.Model.DbContext.GDDBInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.GDDBInfo> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.GDDBInfo;
        }
   
        
 
 }
  

 public partial class HappyRabApp : AppBase<Pos.Model.DbContext.HappyRab> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.HappyRab> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.HappyRab;
        }
   
        
 
 }
  

 public partial class HolidayApp : AppBase<Pos.Model.DbContext.Holiday> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.Holiday> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.Holiday;
        }
   
        
 
 }
  

 public partial class HotFdTypeApp : AppBase<Pos.Model.DbContext.HotFdType> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.HotFdType> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.HotFdType;
        }
   
        
 
 }
  

 public partial class HotFoodApp : AppBase<Pos.Model.DbContext.HotFood> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.HotFood> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.HotFood;
        }
   
        
 
 }
  

 public partial class Inv_TimeSectionApp : AppBase<Pos.Model.DbContext.Inv_TimeSection> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.Inv_TimeSection> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.Inv_TimeSection;
        }
   
        
 
 }
  

 public partial class InvRollBackApp : AppBase<Pos.Model.DbContext.InvRollBack> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.InvRollBack> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.InvRollBack;
        }
   
        
 
 }
  

 public partial class LanIdApp : AppBase<Pos.Model.DbContext.LanId> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.LanId> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.LanId;
        }
   
        
 
 }
  

 public partial class LanStringApp : AppBase<Pos.Model.DbContext.LanString> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.LanString> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.LanString;
        }
   
        
 
 }
  

 public partial class LastInvNoApp : AppBase<Pos.Model.DbContext.LastInvNo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.LastInvNo> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.LastInvNo;
        }
   
        
 
 }
  

 public partial class LastRefNoApp : AppBase<Pos.Model.DbContext.LastRefNo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.LastRefNo> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.LastRefNo;
        }
   
        
 
 }
  

 public partial class meal_distribution_infoApp : AppBase<Pos.Model.DbContext.meal_distribution_info> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.meal_distribution_info> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.meal_distribution_info;
        }
   
        
 
 }
  

 public partial class meal_infoApp : AppBase<Pos.Model.DbContext.meal_info> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.meal_info> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.meal_info;
        }
   
        
 
 }
  

 public partial class MembAmountEditLogApp : AppBase<Pos.Model.DbContext.MembAmountEditLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.MembAmountEditLog> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.MembAmountEditLog;
        }
   
        
 
 }
  

 public partial class MemberApp : AppBase<Pos.Model.DbContext.Member> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.Member> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.Member;
        }
   
        
 
 }
  

 public partial class MemberCheckoutInfoApp : AppBase<Pos.Model.DbContext.MemberCheckoutInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.MemberCheckoutInfo> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.MemberCheckoutInfo;
        }
   
        
 
 }
  

 public partial class MemberGiveSetApp : AppBase<Pos.Model.DbContext.MemberGiveSet> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.MemberGiveSet> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.MemberGiveSet;
        }
   
        
 
 }
  

 public partial class MembSetApp : AppBase<Pos.Model.DbContext.MembSet> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.MembSet> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.MembSet;
        }
   
        
 
 }
  

 public partial class MGradeFdDiscApp : AppBase<Pos.Model.DbContext.MGradeFdDisc> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.MGradeFdDisc> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.MGradeFdDisc;
        }
   
        
 
 }
  

 public partial class MobileFdGiveApp : AppBase<Pos.Model.DbContext.MobileFdGive> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.MobileFdGive> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.MobileFdGive;
        }
   
        
 
 }
  

 public partial class MobileFoodApp : AppBase<Pos.Model.DbContext.MobileFood> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.MobileFood> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.MobileFood;
        }
   
        
 
 }
  

 public partial class MobileFoodDiscApp : AppBase<Pos.Model.DbContext.MobileFoodDisc> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.MobileFoodDisc> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.MobileFoodDisc;
        }
   
        
 
 }
  

 public partial class MobileFtTypeApp : AppBase<Pos.Model.DbContext.MobileFtType> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.MobileFtType> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.MobileFtType;
        }
   
        
 
 }
  

 public partial class MobilePackGiveApp : AppBase<Pos.Model.DbContext.MobilePackGive> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.MobilePackGive> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.MobilePackGive;
        }
   
        
 
 }
  

 public partial class MobilOrderItemApp : AppBase<Pos.Model.DbContext.MobilOrderItem> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.MobilOrderItem> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.MobilOrderItem;
        }
   
        
 
 }
  

 public partial class MobilOrderTitleApp : AppBase<Pos.Model.DbContext.MobilOrderTitle> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.MobilOrderTitle> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.MobilOrderTitle;
        }
   
        
 
 }
  

 public partial class MobilUserOrderTitleApp : AppBase<Pos.Model.DbContext.MobilUserOrderTitle> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.MobilUserOrderTitle> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.MobilUserOrderTitle;
        }
   
        
 
 }
  

 public partial class NewMemberLogApp : AppBase<Pos.Model.DbContext.NewMemberLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.NewMemberLog> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.NewMemberLog;
        }
   
        
 
 }
  

 public partial class ParamSetApp : AppBase<Pos.Model.DbContext.ParamSet> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.ParamSet> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.ParamSet;
        }
   
        
 
 }
  

 public partial class pre_orderApp : AppBase<Pos.Model.DbContext.pre_order> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.pre_order> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.pre_order;
        }
   
        
 
 }
  

 public partial class PreOrderSendMsgInfoApp : AppBase<Pos.Model.DbContext.PreOrderSendMsgInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.PreOrderSendMsgInfo> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.PreOrderSendMsgInfo;
        }
   
        
 
 }
  

 public partial class PriceNoApp : AppBase<Pos.Model.DbContext.PriceNo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.PriceNo> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.PriceNo;
        }
   
        
 
 }
  

 public partial class QrInfoApp : AppBase<Pos.Model.DbContext.QrInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.QrInfo> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.QrInfo;
        }
   
        
 
 }
  

 public partial class RecordRoomTimeApp : AppBase<Pos.Model.DbContext.RecordRoomTime> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.RecordRoomTime> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.RecordRoomTime;
        }
   
        
 
 }
  

 public partial class RefToZDLogApp : AppBase<Pos.Model.DbContext.RefToZDLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.RefToZDLog> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.RefToZDLog;
        }
   
        
 
 }
  

 public partial class RightSetApp : AppBase<Pos.Model.DbContext.RightSet> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.RightSet> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.RightSet;
        }
   
        
 
 }
  

 public partial class RmAccountInfoApp : AppBase<Pos.Model.DbContext.RmAccountInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.RmAccountInfo> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.RmAccountInfo;
        }
   
        
 
 }
  

 public partial class RmAreaApp : AppBase<Pos.Model.DbContext.RmArea> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.RmArea> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.RmArea;
        }
   
        
 
 }
  

 public partial class RmClearLogApp : AppBase<Pos.Model.DbContext.RmClearLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.RmClearLog> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.RmClearLog;
        }
   
        
 
 }
  

 public partial class RmCloseInfoApp : AppBase<Pos.Model.DbContext.RmCloseInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.RmCloseInfo> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.RmCloseInfo;
        }
   
        
 
 }
  

 public partial class RmCloseInfo_CollectApp : AppBase<Pos.Model.DbContext.RmCloseInfo_Collect> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.RmCloseInfo_Collect> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.RmCloseInfo_Collect;
        }
   
        
 
 }
  

 public partial class RmExchangeDetailApp : AppBase<Pos.Model.DbContext.RmExchangeDetail> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.RmExchangeDetail> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.RmExchangeDetail;
        }
   
        
 
 }
  

 public partial class RmExchangeLogApp : AppBase<Pos.Model.DbContext.RmExchangeLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.RmExchangeLog> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.RmExchangeLog;
        }
   
        
 
 }
  

 public partial class RmFtPrnIndexApp : AppBase<Pos.Model.DbContext.RmFtPrnIndex> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.RmFtPrnIndex> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.RmFtPrnIndex;
        }
   
        
 
 }
  

 public partial class RmOrderApp : AppBase<Pos.Model.DbContext.RmOrder> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.RmOrder> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.RmOrder;
        }
   
        
 
 }
  

 public partial class RmOrderDelLogApp : AppBase<Pos.Model.DbContext.RmOrderDelLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.RmOrderDelLog> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.RmOrderDelLog;
        }
   
        
 
 }
  

 public partial class RmOrderLogApp : AppBase<Pos.Model.DbContext.RmOrderLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.RmOrderLog> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.RmOrderLog;
        }
   
        
 
 }
  

 public partial class RmsRoomApp : AppBase<Pos.Model.DbContext.RmsRoom> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.RmsRoom> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.RmsRoom;
        }
   
        
 
 }
  

 public partial class RmTypeApp : AppBase<Pos.Model.DbContext.RmType> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.RmType> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.RmType;
        }
   
        
 
 }
  

 public partial class RoomApp : AppBase<Pos.Model.DbContext.Room> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.Room> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.Room;
        }
   
        
 
 }
  

 public partial class RoomCommissionApp : AppBase<Pos.Model.DbContext.RoomCommission> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.RoomCommission> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.RoomCommission;
        }
   
        
 
 }
  

 public partial class RoomExtendApp : AppBase<Pos.Model.DbContext.RoomExtend> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.RoomExtend> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.RoomExtend;
        }
   
        
 
 }
  

 public partial class RoomtestApp : AppBase<Pos.Model.DbContext.Roomtest> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.Roomtest> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.Roomtest;
        }
   
        
 
 }
  

 public partial class RtAutoApp : AppBase<Pos.Model.DbContext.RtAuto> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.RtAuto> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.RtAuto;
        }
   
        
 
 }
  

 public partial class RtAutoZDApp : AppBase<Pos.Model.DbContext.RtAutoZD> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.RtAutoZD> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.RtAutoZD;
        }
   
        
 
 }
  

 public partial class RtTimePriceApp : AppBase<Pos.Model.DbContext.RtTimePrice> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.RtTimePrice> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.RtTimePrice;
        }
   
        
 
 }
  

 public partial class S_AccTypeApp : AppBase<Pos.Model.DbContext.S_AccType> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.S_AccType> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.S_AccType;
        }
   
        
 
 }
  

 public partial class S_CashItemApp : AppBase<Pos.Model.DbContext.S_CashItem> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.S_CashItem> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.S_CashItem;
        }
   
        
 
 }
  

 public partial class S_PrnTypeApp : AppBase<Pos.Model.DbContext.S_PrnType> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.S_PrnType> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.S_PrnType;
        }
   
        
 
 }
  

 public partial class S_RmStatusApp : AppBase<Pos.Model.DbContext.S_RmStatus> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.S_RmStatus> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.S_RmStatus;
        }
   
        
 
 }
  

 public partial class SchedulingRecordApp : AppBase<Pos.Model.DbContext.SchedulingRecord> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.SchedulingRecord> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.SchedulingRecord;
        }
   
        
 
 }
  

 public partial class SDateApp : AppBase<Pos.Model.DbContext.SDate> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.SDate> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.SDate;
        }
   
        
 
 }
  

 public partial class ShareSetInfoApp : AppBase<Pos.Model.DbContext.ShareSetInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.ShareSetInfo> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.ShareSetInfo;
        }
   
        
 
 }
  

 public partial class ShiftInfoApp : AppBase<Pos.Model.DbContext.ShiftInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.ShiftInfo> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.ShiftInfo;
        }
   
        
 
 }
  

 public partial class StarInfoApp : AppBase<Pos.Model.DbContext.StarInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.StarInfo> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.StarInfo;
        }
   
        
 
 }
  

 public partial class TestTableApp : AppBase<Pos.Model.DbContext.TestTable> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.TestTable> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.TestTable;
        }
   
        
 
 }
  

 public partial class Th_RoomCommissionAllotApp : AppBase<Pos.Model.DbContext.Th_RoomCommissionAllot> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.Th_RoomCommissionAllot> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.Th_RoomCommissionAllot;
        }
   
        
 
 }
  

 public partial class TimeZoneApp : AppBase<Pos.Model.DbContext.TimeZone> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.TimeZone> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.TimeZone;
        }
   
        
 
 }
  

 public partial class triggerRecordApp : AppBase<Pos.Model.DbContext.triggerRecord> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.triggerRecord> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.triggerRecord;
        }
   
        
 
 }
  

 public partial class UserAmountApp : AppBase<Pos.Model.DbContext.UserAmount> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.UserAmount> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.UserAmount;
        }
   
        
 
 }
  

 public partial class UserAmountDetailApp : AppBase<Pos.Model.DbContext.UserAmountDetail> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.UserAmountDetail> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.UserAmountDetail;
        }
   
        
 
 }
  

 public partial class UserFtZDApp : AppBase<Pos.Model.DbContext.UserFtZD> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.UserFtZD> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.UserFtZD;
        }
   
        
 
 }
  

 public partial class UserIOApp : AppBase<Pos.Model.DbContext.UserIO> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.UserIO> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.UserIO;
        }
   
        
 
 }
  

 public partial class UserZDItemApp : AppBase<Pos.Model.DbContext.UserZDItem> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.UserZDItem> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.UserZDItem;
        }
   
        
 
 }
  

 public partial class UserZDItemDetailApp : AppBase<Pos.Model.DbContext.UserZDItemDetail> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.UserZDItemDetail> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.UserZDItemDetail;
        }
   
        
 
 }
  

 public partial class UserZDSetApp : AppBase<Pos.Model.DbContext.UserZDSet> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.UserZDSet> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.UserZDSet;
        }
   
        
 
 }
  

 public partial class VesaApp : AppBase<Pos.Model.DbContext.Vesa> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.Vesa> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.Vesa;
        }
   
        
 
 }
  

 public partial class WebOrderTableApp : AppBase<Pos.Model.DbContext.WebOrderTable> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.WebOrderTable> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.WebOrderTable;
        }
   
        
 
 }
  

 public partial class WeChatFoodOrderMsgApp : AppBase<Pos.Model.DbContext.WeChatFoodOrderMsg> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.WeChatFoodOrderMsg> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.WeChatFoodOrderMsg;
        }
   
        
 
 }
  

 public partial class WeChatFoodOrderMsg2App : AppBase<Pos.Model.DbContext.WeChatFoodOrderMsg2> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.WeChatFoodOrderMsg2> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.WeChatFoodOrderMsg2;
        }
   
        
 
 }
  

 public partial class WindTicketApp : AppBase<Pos.Model.DbContext.WindTicket> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.WindTicket> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.WindTicket;
        }
   
        
 
 }
  

 public partial class wx_shopmall_worktimeApp : AppBase<Pos.Model.DbContext.wx_shopmall_worktime> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.wx_shopmall_worktime> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.wx_shopmall_worktime;
        }
   
        
 
 }
  

 public partial class wxPayCheckInfoApp : AppBase<Pos.Model.DbContext.wxPayCheckInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.wxPayCheckInfo> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.wxPayCheckInfo;
        }
   
        
 
 }
  

 public partial class wxPayInfoApp : AppBase<Pos.Model.DbContext.wxPayInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos.Model.DbContext.wxPayInfo> SetRepository(RepositoryFactory.RepositorySession Session)
        {
            return Session.wxPayInfo;
        }
   
        
 
 }
  

}
