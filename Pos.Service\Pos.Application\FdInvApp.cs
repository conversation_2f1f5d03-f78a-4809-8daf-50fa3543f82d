﻿using Pos.Model.DbContext;
using Pos.Model.DbFood.Conntext;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Pos.Application
{
    public partial class FdInvApp : AppBase<FdInv>
    {
        public bool PlaceOrder(PlaceOrderContext model)
        {
            if (model == null)
                return false;

            if (model.CashUserId == null && !string.IsNullOrEmpty(model.InputUserId))
                model.CashUserId = model.InputUserId;

            return Repository.FdInv.PlaceOrder(model);
        }
    }
}
