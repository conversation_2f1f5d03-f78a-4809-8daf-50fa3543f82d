﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{0752C409-20CA-41B4-B9B3-BEBC6D1602BD}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Pos.Model</RootNamespace>
    <AssemblyName>Pos.Model</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ComponentApplicationServiceInterface">
      <HintPath>Package\ComponentApplicationServiceInterface.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework">
      <HintPath>..\packages\EntityFramework.5.0.0\lib\net40\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.Entity" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DbContext\FoodLabel.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbFood\Conntext\FoodContext.cs" />
    <Compile Include="DbContext\AddItem.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\AiType.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\AmountLog.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\CarLeaveLog.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\Cashier.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\ClearDataLog.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\DeadLockLog.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\DepositInfo.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\Dept.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\DeptBanSet.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\dtproperties.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FdCash.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FdCashBak.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FdCashBak_B.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FdCashBak_Bak.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FdCash_Track.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FdDetType.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FdImage.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FdInv.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FdInvCashItem.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FdInvDesc.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FdInv_B.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FdInv_Bak.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FdInv_ExchangeLog.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FdTicket.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FdTimePrice.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FdTimeZone.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FdType.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FdUser.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FdUserGrade.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FdUserRights.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FestivalTime.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\Food.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FoodCal.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FoodDB.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>FoodDB.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FoodDB.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FoodDB.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>FoodDB.edmx</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FoodOrderM.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FPrn.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FPrnData.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FPrnData_Bak.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FreePackageCoupon_Record.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\FtInfo.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\GDDB20Info.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\GDDBInfo.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\HappyRab.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\Holiday.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\HotFdType.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\HotFood.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\InvRollBack.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\Inv_TimeSection.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\LanId.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\LanString.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\LastInvNo.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\LastRefNo.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\meal_distribution_info.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\meal_info.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\MembAmountEditLog.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\Member.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\MemberCheckoutInfo.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\MemberGiveSet.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\MembSet.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\MGradeFdDisc.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\MobileFdGive.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\MobileFood.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\MobileFoodDisc.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\MobileFtType.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\MobilePackGive.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\MobilOrderItem.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\MobilOrderTitle.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\MobilUserOrderTitle.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\NewMemberLog.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\ParamSet.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\PreOrderSendMsgInfo.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\pre_order.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\PriceNo.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\QrInfo.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\RecordRoomTime.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\RefToZDLog.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\RightSet.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\RmAccountInfo.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\RmArea.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\RmClearLog.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\RmCloseInfo.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\RmCloseInfo_Collect.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\RmExchangeDetail.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\RmExchangeLog.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\RmFtPrnIndex.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\RmOrder.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\RmOrderDelLog.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\RmOrderLog.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\RmsRoom.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\RmType.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\Room.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\RoomCommission.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\RoomExtend.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\Roomtest.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\RtAuto.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\RtAutoZD.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\RtTimePrice.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\SchedulingRecord.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\SDate.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\ShareSetInfo.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\ShiftInfo.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\StarInfo.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\S_AccType.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\S_CashItem.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\S_PrnType.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\S_RmStatus.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\TestTable.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\Th_RoomCommissionAllot.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\TimeZone.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\triggerRecord.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\UserAmount.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\UserAmountDetail.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\UserFtZD.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\UserIO.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\UserZDItem.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\UserZDItemDetail.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\UserZDSet.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\Vesa.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\WebOrderTable.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\WeChatFoodOrderMsg.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\WeChatFoodOrderMsg2.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\WindTicket.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\wxPayCheckInfo.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\wxPayInfo.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbContext\wx_shopmall_worktime.cs">
      <DependentUpon>FoodDB.tt</DependentUpon>
    </Compile>
    <Compile Include="DbFood\Conntext\FoodLabelContext.cs" />
    <Compile Include="DbFood\Conntext\PlaceOrderContext.cs" />
    <Compile Include="DbFood\Conntext\RoomContext.cs" />
    <Compile Include="DbFood\RespModel\GetFoodLabelListModel.cs" />
    <Compile Include="DbFood\RespModel\RoomModel.cs" />
    <Compile Include="DbFood\RespModel\WaitCreateModel.cs" />
    <Compile Include="Enum\FdLabelCategoryEnum.cs" />
    <Compile Include="Enum\FdLabelTypeEnum.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="DbFood\RespModel\FoodModel.cs" />
  </ItemGroup>
  <ItemGroup>
    <EntityDeploy Include="DbContext\FoodDB.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>FoodDB.Designer.cs</LastGenOutput>
    </EntityDeploy>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.Config" />
    <None Include="DbContext\FoodDB.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>FoodDB.edmx</DependentUpon>
      <LastGenOutput>FoodDB.Context.cs</LastGenOutput>
    </None>
    <None Include="DbContext\FoodDB.edmx.diagram">
      <DependentUpon>FoodDB.edmx</DependentUpon>
    </None>
    <None Include="DbContext\FoodDB.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>FoodDB.edmx</DependentUpon>
      <LastGenOutput>FoodDB.cs</LastGenOutput>
    </None>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Context\" />
    <Folder Include="RespModel\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Package\ComponentApplicationServiceInterface.dll" />
    <Content Include="Package\ComponentCore.dll" />
    <Content Include="Package\EntityFramework.dll" />
    <Content Include="Package\SERVICE.PROXY.dll" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>