﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Pos.Model.DbFood.Conntext
{
    public class PlaceOrderContext
    {
        /// <summary>
        /// 房间号
        /// </summary>
        public string RmNo { get; set; }

        /// <summary>
        /// 消费模式
        /// </summary>
        public string CashType { get; set; }

        public string CashUserId { get; set; }

        public string InputUserId { get; set; }

        /// <summary>
        /// 订单商品明细
        /// </summary>
        public List<OrderItem> Items { get; set; }
    }

    public class OrderItem
    {
        /// <summary>
        /// 商品明细
        /// </summary>
        public string FdNo { get; set; }

        /// <summary>
        /// 商品数量
        /// </summary>
        public int FdQty { get; set; }

        /// <summary>
        /// 商品价格
        /// </summary>
        public int FdPrice { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Ai { get; set; }
    }

    public class GetOrderNumbersContext
    {
        /// <summary>
        /// 天王下单编号
        /// </summary>
        public string RmNo { get; set; }
    }
}
