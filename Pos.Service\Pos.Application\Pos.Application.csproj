﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{1EC5741D-6CD4-401F-A868-E4D6DF406142}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Pos.Application</RootNamespace>
    <AssemblyName>Pos.Application</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ComponentApplicationServiceInterface, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Pos.Model\Package\ComponentApplicationServiceInterface.dll</HintPath>
    </Reference>
    <Reference Include="ComponentCore, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Pos.Model\Package\ComponentCore.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AppBase.cs" />
    <Compile Include="FdInvApp.cs" />
    <Compile Include="FoodApp.cs" />
    <Compile Include="FoodLabelApp.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="RoomApp.cs" />
    <Compile Include="T4Application.cs">
      <DependentUpon>T4Application.tt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="T4Application.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>T4Application.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Pos.Domain\Pos.Domain.csproj">
      <Project>{48b91ac4-ed2f-48c4-84ff-263cfe5ab65a}</Project>
      <Name>Pos.Domain</Name>
    </ProjectReference>
    <ProjectReference Include="..\Pos.Model\Pos.Model.csproj">
      <Project>{0752c409-20ca-41b4-b9b3-bebc6d1602bd}</Project>
      <Name>Pos.Model</Name>
    </ProjectReference>
    <ProjectReference Include="..\Pos.RepositoryFactory\Pos.RepositoryFactory.csproj">
      <Project>{dd0c11d5-57bd-4148-8175-656fad45dc5b}</Project>
      <Name>Pos.RepositoryFactory</Name>
    </ProjectReference>
    <ProjectReference Include="..\Pos.Repository\Pos.Repository.csproj">
      <Project>{a62d073e-370a-48e0-867c-3416be196b23}</Project>
      <Name>Pos.Repository</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>