﻿using Pos.Domain.IRepository;
using Pos.Model.DbContext;
using Pos.Model.DbFood.Conntext;
using Pos.Model.DbFood.RespModel;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace Pos.Repository
{
    public partial class RoomRepository : BaseRepository<Room>, IRoomRepository
    {
        public GetRoomInfoModel GetRoomInfo(GetRoomInfoContext context)
        {
            var sql = "Select top 1 RmNo,RmStatus,InvNo from Room WHERE RmNo = @RmNo";
            var param = new List<SqlParameter>()
            {
                new SqlParameter("@RmNo",context.RmNo),
            };

            var result = db.Database.SqlQuery<GetRoomInfoModel>(sql, param.ToArray()).FirstOrDefault();
            if (result == null)
                throw new ExMessage("房间不存在！");

            result.IsOrder = result.RmStatus == "U" || result.RmStatus == "C";
            return result;
        }
    }
}
