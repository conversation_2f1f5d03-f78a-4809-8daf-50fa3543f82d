﻿using System;
using System.Collections.Generic;
using Pos.Model.DbFood.RespModel;
using Pos.Model.DbContext;
using System.Linq;
using System.Text;
using System.Reflection;
using Pos.Common;

namespace Pos.Drive.DbFood.ProductCalculation
{
    /// <summary>
    /// 统一计算实现类
    /// </summary>
    public class UnifiedComputingSystem : DriveBase
    {
        /// <summary>
        /// 统一计算
        /// </summary>
        /// <param name="storeData">导入数据</param>
        /// <param name="param">根据Label表的字段进行筛选</param>
        /// <param name="countType">方法名称</param>
        /// <param name="parameters">方法参数</param>
        public List<GetStoreReportModel> UnifiedCompute(List<StoreReportModel> storeData, List<string> param, string countType = "default", object[] parameters = null)
        {
            List<GetStoreReportModel> reportData = new List<GetStoreReportModel>();
            var foodLabel = typeof(FoodLabel);
            var properties = foodLabel.GetProperties().Select(x => x.Name).ToList();
            if (storeData.Count <= 0)
                throw new Exception("无统一计算数据！");
            if (param.Count <= 0)
                throw new Exception("请选择筛选字段进行计算！");
            if (param.Except(properties).Count() > 0)
                throw new Exception("筛选字段有误，数据表中不存在！");

            var fdIds = storeData.Select(x => x.FdNo).Distinct().ToList();
            var foodLabelList = app.FoodLabel.IQueryable(x => fdIds.Contains(x.FdNo)).ToList();

            ProductCalculationBase calculation = null;
            foreach (var item in param)
            {
                calculation = (ProductCalculationBase)CreateInstance("Pos.Drive", "Pos.Drive.DbFood.ProductCalculation.Impl", "Count_" + item);
                if (countType == "default")
                    reportData.AddRange(calculation.UnifiedCompute(storeData, foodLabelList).DividedCalculate());
                else
                {
                    var type = typeof(ProductCalculationBase);
                    if (type.GetMethods().Any(x => x.Name == countType))//如果有输入的方法，就走输入的方法，如果没有就走默认方法
                    {
                        var method = type.GetMethods().FirstOrDefault(x => x.Name == countType);
                        if (method != null)
                        {
                            reportData.AddRange((List<GetStoreReportModel>)method.Invoke(calculation, parameters));
                        }
                    }
                    else
                        reportData.AddRange(calculation.UnifiedCompute(storeData, foodLabelList).DividedCalculate());
                }
            }

            reportData = reportData.GroupBy(x => x.FtNo).Select(x => new GetStoreReportModel()
            {
                FtNo = x.Key,
                FtCName = x.FirstOrDefault().FtCName,
                TotalAmount = x.Sum(w => w.TotalAmount),
                BarDivision = x.Sum(w => w.BarDivision),
                NightDivision = x.Sum(w => w.NightDivision),
                InfieldDivision = x.Sum(w => w.InfieldDivision),
                BuffetDivision = x.Sum(w => w.BuffetDivision)
            }).ToList();

            return reportData;
        }

        private static object CreateInstance(string assemblyName, string nameSpace, string className)
        {
            try
            {
                string fullName = nameSpace + "." + className;//命名空间.类型名
                object ect = Assembly.Load(assemblyName).CreateInstance(fullName, false, BindingFlags.Default, null, null, null, null);//加载程序集，创建程序集里面的 命名空间.类型名 实例
                if (ect == null)
                {
                    fullName = nameSpace + "." + "Count_Default";
                    ect = Assembly.Load(assemblyName).CreateInstance(fullName, false, BindingFlags.Default, null, null, null, null);
                }

                return ect;//类型转换并返回
            }
            catch (Exception ex) { Console.WriteLine(ex.Message + "===" + ex.StackTrace); return false; }
        }
    }
}
