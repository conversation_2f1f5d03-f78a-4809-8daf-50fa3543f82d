﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using System.Text;

namespace Pos.Common
{
    public static class EnumHelper
    {
        /// <summary>
        /// 将枚举类型转换成List集合
        /// </summary>
        /// <param name="enumType"></param>
        /// <returns></returns>
        public static List<KeyValuePair<int, string>> GetEnumList(this Type enumType)
        {
            var dic = new List<KeyValuePair<int, string>>();

            if (!enumType.IsEnum)
                throw new Exception("非枚举类型！");

            var enumValues = Enum.GetValues(enumType);
            foreach (Enum item in enumValues)
            {
                string description = item.GetEnumDescription();
                dic.Add(new KeyValuePair<int, string>((int)Enum.Parse(enumType, item.ToString()), description));
            }

            return dic;
        }

        public static string GetEnumDescription(this Enum value)
        {
            FieldInfo fieldInfo = value.GetType().GetField(value.ToString());
            object[] attributes = fieldInfo.GetCustomAttributes(typeof(DescriptionAttribute), false);
            if (attributes.Length > 0)
                return ((DescriptionAttribute)attributes[0]).Description;
            else
                return fieldInfo.Name;
        }

        /// <summary>
        /// 根据description获取枚举的Value值
        /// </summary>
        /// <param name="description"></param>
        /// <returns></returns>
        public static int GetValue<T>(string description)
        {
            foreach (var field in typeof(T).GetFields())
            {
                if (Attribute.GetCustomAttribute(field, typeof(DescriptionAttribute)) is DescriptionAttribute attribute)
                {
                    if (attribute.Description == description)
                    {
                        return (int)field.GetValue(null);
                    }
                }
            }
            return 0;
        }
    }
}
