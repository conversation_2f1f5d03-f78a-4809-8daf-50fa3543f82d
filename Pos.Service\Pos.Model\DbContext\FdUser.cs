//------------------------------------------------------------------------------
// <auto-generated>
//    此代码是根据模板生成的。
//
//    手动更改此文件可能会导致应用程序中发生异常行为。
//    如果重新生成代码，则将覆盖对此文件的手动更改。
// </auto-generated>
//------------------------------------------------------------------------------

namespace Pos.Model.DbContext
{
    using System;
    using System.Collections.Generic;
    
    public partial class FdUser
    {
        public FdUser()
        {
            this.AmountLog = new HashSet<AmountLog>();
            this.FdUserRights = new HashSet<FdUserRights>();
            this.UserFtZD = new HashSet<UserFtZD>();
        }
    
        public string UserId { get; set; }
        public string CName { get; set; }
        public string EName { get; set; }
        public string CardNo { get; set; }
        public string Sex { get; set; }
        public Nullable<short> Height { get; set; }
        public Nullable<short> Weight { get; set; }
        public string LocalAddr { get; set; }
        public string HomeAddr { get; set; }
        public string Id { get; set; }
        public string Birthday { get; set; }
        public string Tel { get; set; }
        public string InDate { get; set; }
        public string Job { get; set; }
        public string DanBao { get; set; }
        public bool Catonese { get; set; }
        public bool Chinese { get; set; }
        public bool English { get; set; }
        public string Intrductor { get; set; }
        public string PriorCompany { get; set; }
        public string PriorJob { get; set; }
        public Nullable<int> InSalary { get; set; }
        public Nullable<int> Salary { get; set; }
        public bool Marry { get; set; }
        public int DayZDLimit { get; set; }
        public int MonthZDLimit { get; set; }
        public bool CheckAmount { get; set; }
        public int CashAmount { get; set; }
        public int CashAmountLeft { get; set; }
        public string PassWord { get; set; }
        public byte[] Photo { get; set; }
        public string DeptId { get; set; }
        public string BanNo { get; set; }
        public int Tag { get; set; }
        public int MinDiscRate { get; set; }
        public int WeekZDLimit { get; set; }
        public System.Guid rowguid { get; set; }
        public System.Guid msrepl_tran_version { get; set; }
    
        public virtual ICollection<AmountLog> AmountLog { get; set; }
        public virtual ICollection<FdUserRights> FdUserRights { get; set; }
        public virtual ICollection<UserFtZD> UserFtZD { get; set; }
        public virtual UserZDSet UserZDSet { get; set; }
    }
}
