﻿

using Pos.Domain.IRepository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Pos.RepositoryFactory
{
 public partial class RepositorySession {

  IAddItemRepository _AddItem;
    public IAddItemRepository AddItem
        {
            get
            {
                if (_AddItem == null) _AddItem = AbstractRepositoryFactory.Create_AddItem();
                return _AddItem;
            }
        }
        
 

  


  IAiTypeRepository _AiType;
    public IAiTypeRepository AiType
        {
            get
            {
                if (_AiType == null) _AiType = AbstractRepositoryFactory.Create_AiType();
                return _AiType;
            }
        }
        
 

  


  IAmountLogRepository _AmountLog;
    public IAmountLogRepository AmountLog
        {
            get
            {
                if (_AmountLog == null) _AmountLog = AbstractRepositoryFactory.Create_AmountLog();
                return _AmountLog;
            }
        }
        
 

  


  ICarLeaveLogRepository _CarLeaveLog;
    public ICarLeaveLogRepository CarLeaveLog
        {
            get
            {
                if (_CarLeaveLog == null) _CarLeaveLog = AbstractRepositoryFactory.Create_CarLeaveLog();
                return _CarLeaveLog;
            }
        }
        
 

  


  ICashierRepository _Cashier;
    public ICashierRepository Cashier
        {
            get
            {
                if (_Cashier == null) _Cashier = AbstractRepositoryFactory.Create_Cashier();
                return _Cashier;
            }
        }
        
 

  


  IClearDataLogRepository _ClearDataLog;
    public IClearDataLogRepository ClearDataLog
        {
            get
            {
                if (_ClearDataLog == null) _ClearDataLog = AbstractRepositoryFactory.Create_ClearDataLog();
                return _ClearDataLog;
            }
        }
        
 

  


  IDeadLockLogRepository _DeadLockLog;
    public IDeadLockLogRepository DeadLockLog
        {
            get
            {
                if (_DeadLockLog == null) _DeadLockLog = AbstractRepositoryFactory.Create_DeadLockLog();
                return _DeadLockLog;
            }
        }
        
 

  


  IDepositInfoRepository _DepositInfo;
    public IDepositInfoRepository DepositInfo
        {
            get
            {
                if (_DepositInfo == null) _DepositInfo = AbstractRepositoryFactory.Create_DepositInfo();
                return _DepositInfo;
            }
        }
        
 

  


  IDeptRepository _Dept;
    public IDeptRepository Dept
        {
            get
            {
                if (_Dept == null) _Dept = AbstractRepositoryFactory.Create_Dept();
                return _Dept;
            }
        }
        
 

  


  IDeptBanSetRepository _DeptBanSet;
    public IDeptBanSetRepository DeptBanSet
        {
            get
            {
                if (_DeptBanSet == null) _DeptBanSet = AbstractRepositoryFactory.Create_DeptBanSet();
                return _DeptBanSet;
            }
        }
        
 

  


  IdtpropertiesRepository _dtproperties;
    public IdtpropertiesRepository dtproperties
        {
            get
            {
                if (_dtproperties == null) _dtproperties = AbstractRepositoryFactory.Create_dtproperties();
                return _dtproperties;
            }
        }
        
 

  


  IFdCashRepository _FdCash;
    public IFdCashRepository FdCash
        {
            get
            {
                if (_FdCash == null) _FdCash = AbstractRepositoryFactory.Create_FdCash();
                return _FdCash;
            }
        }
        
 

  


  IFdCash_TrackRepository _FdCash_Track;
    public IFdCash_TrackRepository FdCash_Track
        {
            get
            {
                if (_FdCash_Track == null) _FdCash_Track = AbstractRepositoryFactory.Create_FdCash_Track();
                return _FdCash_Track;
            }
        }
        
 

  


  IFdCashBakRepository _FdCashBak;
    public IFdCashBakRepository FdCashBak
        {
            get
            {
                if (_FdCashBak == null) _FdCashBak = AbstractRepositoryFactory.Create_FdCashBak();
                return _FdCashBak;
            }
        }
        
 

  


  IFdCashBak_BRepository _FdCashBak_B;
    public IFdCashBak_BRepository FdCashBak_B
        {
            get
            {
                if (_FdCashBak_B == null) _FdCashBak_B = AbstractRepositoryFactory.Create_FdCashBak_B();
                return _FdCashBak_B;
            }
        }
        
 

  


  IFdCashBak_BakRepository _FdCashBak_Bak;
    public IFdCashBak_BakRepository FdCashBak_Bak
        {
            get
            {
                if (_FdCashBak_Bak == null) _FdCashBak_Bak = AbstractRepositoryFactory.Create_FdCashBak_Bak();
                return _FdCashBak_Bak;
            }
        }
        
 

  


  IFdDetTypeRepository _FdDetType;
    public IFdDetTypeRepository FdDetType
        {
            get
            {
                if (_FdDetType == null) _FdDetType = AbstractRepositoryFactory.Create_FdDetType();
                return _FdDetType;
            }
        }
        
 

  


  IFdImageRepository _FdImage;
    public IFdImageRepository FdImage
        {
            get
            {
                if (_FdImage == null) _FdImage = AbstractRepositoryFactory.Create_FdImage();
                return _FdImage;
            }
        }
        
 

  


  IFdInvRepository _FdInv;
    public IFdInvRepository FdInv
        {
            get
            {
                if (_FdInv == null) _FdInv = AbstractRepositoryFactory.Create_FdInv();
                return _FdInv;
            }
        }
        
 

  


  IFdInv_BRepository _FdInv_B;
    public IFdInv_BRepository FdInv_B
        {
            get
            {
                if (_FdInv_B == null) _FdInv_B = AbstractRepositoryFactory.Create_FdInv_B();
                return _FdInv_B;
            }
        }
        
 

  


  IFdInv_BakRepository _FdInv_Bak;
    public IFdInv_BakRepository FdInv_Bak
        {
            get
            {
                if (_FdInv_Bak == null) _FdInv_Bak = AbstractRepositoryFactory.Create_FdInv_Bak();
                return _FdInv_Bak;
            }
        }
        
 

  


  IFdInv_ExchangeLogRepository _FdInv_ExchangeLog;
    public IFdInv_ExchangeLogRepository FdInv_ExchangeLog
        {
            get
            {
                if (_FdInv_ExchangeLog == null) _FdInv_ExchangeLog = AbstractRepositoryFactory.Create_FdInv_ExchangeLog();
                return _FdInv_ExchangeLog;
            }
        }
        
 

  


  IFdInvCashItemRepository _FdInvCashItem;
    public IFdInvCashItemRepository FdInvCashItem
        {
            get
            {
                if (_FdInvCashItem == null) _FdInvCashItem = AbstractRepositoryFactory.Create_FdInvCashItem();
                return _FdInvCashItem;
            }
        }
        
 

  


  IFdInvDescRepository _FdInvDesc;
    public IFdInvDescRepository FdInvDesc
        {
            get
            {
                if (_FdInvDesc == null) _FdInvDesc = AbstractRepositoryFactory.Create_FdInvDesc();
                return _FdInvDesc;
            }
        }
        
 

  


  IFdTicketRepository _FdTicket;
    public IFdTicketRepository FdTicket
        {
            get
            {
                if (_FdTicket == null) _FdTicket = AbstractRepositoryFactory.Create_FdTicket();
                return _FdTicket;
            }
        }
        
 

  


  IFdTimePriceRepository _FdTimePrice;
    public IFdTimePriceRepository FdTimePrice
        {
            get
            {
                if (_FdTimePrice == null) _FdTimePrice = AbstractRepositoryFactory.Create_FdTimePrice();
                return _FdTimePrice;
            }
        }
        
 

  


  IFdTimeZoneRepository _FdTimeZone;
    public IFdTimeZoneRepository FdTimeZone
        {
            get
            {
                if (_FdTimeZone == null) _FdTimeZone = AbstractRepositoryFactory.Create_FdTimeZone();
                return _FdTimeZone;
            }
        }
        
 

  


  IFdTypeRepository _FdType;
    public IFdTypeRepository FdType
        {
            get
            {
                if (_FdType == null) _FdType = AbstractRepositoryFactory.Create_FdType();
                return _FdType;
            }
        }
        
 

  


  IFdUserRepository _FdUser;
    public IFdUserRepository FdUser
        {
            get
            {
                if (_FdUser == null) _FdUser = AbstractRepositoryFactory.Create_FdUser();
                return _FdUser;
            }
        }
        
 

  


  IFdUserGradeRepository _FdUserGrade;
    public IFdUserGradeRepository FdUserGrade
        {
            get
            {
                if (_FdUserGrade == null) _FdUserGrade = AbstractRepositoryFactory.Create_FdUserGrade();
                return _FdUserGrade;
            }
        }
        
 

  


  IFdUserRightsRepository _FdUserRights;
    public IFdUserRightsRepository FdUserRights
        {
            get
            {
                if (_FdUserRights == null) _FdUserRights = AbstractRepositoryFactory.Create_FdUserRights();
                return _FdUserRights;
            }
        }
        
 

  


  IFestivalTimeRepository _FestivalTime;
    public IFestivalTimeRepository FestivalTime
        {
            get
            {
                if (_FestivalTime == null) _FestivalTime = AbstractRepositoryFactory.Create_FestivalTime();
                return _FestivalTime;
            }
        }
        
 

  


  IFoodRepository _Food;
    public IFoodRepository Food
        {
            get
            {
                if (_Food == null) _Food = AbstractRepositoryFactory.Create_Food();
                return _Food;
            }
        }
        
 

  


  IFoodCalRepository _FoodCal;
    public IFoodCalRepository FoodCal
        {
            get
            {
                if (_FoodCal == null) _FoodCal = AbstractRepositoryFactory.Create_FoodCal();
                return _FoodCal;
            }
        }
        
 

  


  IFoodLabelRepository _FoodLabel;
    public IFoodLabelRepository FoodLabel
        {
            get
            {
                if (_FoodLabel == null) _FoodLabel = AbstractRepositoryFactory.Create_FoodLabel();
                return _FoodLabel;
            }
        }
        
 

  


  IFoodOrderMRepository _FoodOrderM;
    public IFoodOrderMRepository FoodOrderM
        {
            get
            {
                if (_FoodOrderM == null) _FoodOrderM = AbstractRepositoryFactory.Create_FoodOrderM();
                return _FoodOrderM;
            }
        }
        
 

  


  IFPrnRepository _FPrn;
    public IFPrnRepository FPrn
        {
            get
            {
                if (_FPrn == null) _FPrn = AbstractRepositoryFactory.Create_FPrn();
                return _FPrn;
            }
        }
        
 

  


  IFPrnDataRepository _FPrnData;
    public IFPrnDataRepository FPrnData
        {
            get
            {
                if (_FPrnData == null) _FPrnData = AbstractRepositoryFactory.Create_FPrnData();
                return _FPrnData;
            }
        }
        
 

  


  IFPrnData_BakRepository _FPrnData_Bak;
    public IFPrnData_BakRepository FPrnData_Bak
        {
            get
            {
                if (_FPrnData_Bak == null) _FPrnData_Bak = AbstractRepositoryFactory.Create_FPrnData_Bak();
                return _FPrnData_Bak;
            }
        }
        
 

  


  IFreePackageCoupon_RecordRepository _FreePackageCoupon_Record;
    public IFreePackageCoupon_RecordRepository FreePackageCoupon_Record
        {
            get
            {
                if (_FreePackageCoupon_Record == null) _FreePackageCoupon_Record = AbstractRepositoryFactory.Create_FreePackageCoupon_Record();
                return _FreePackageCoupon_Record;
            }
        }
        
 

  


  IFtInfoRepository _FtInfo;
    public IFtInfoRepository FtInfo
        {
            get
            {
                if (_FtInfo == null) _FtInfo = AbstractRepositoryFactory.Create_FtInfo();
                return _FtInfo;
            }
        }
        
 

  


  IGDDB20InfoRepository _GDDB20Info;
    public IGDDB20InfoRepository GDDB20Info
        {
            get
            {
                if (_GDDB20Info == null) _GDDB20Info = AbstractRepositoryFactory.Create_GDDB20Info();
                return _GDDB20Info;
            }
        }
        
 

  


  IGDDBInfoRepository _GDDBInfo;
    public IGDDBInfoRepository GDDBInfo
        {
            get
            {
                if (_GDDBInfo == null) _GDDBInfo = AbstractRepositoryFactory.Create_GDDBInfo();
                return _GDDBInfo;
            }
        }
        
 

  


  IHappyRabRepository _HappyRab;
    public IHappyRabRepository HappyRab
        {
            get
            {
                if (_HappyRab == null) _HappyRab = AbstractRepositoryFactory.Create_HappyRab();
                return _HappyRab;
            }
        }
        
 

  


  IHolidayRepository _Holiday;
    public IHolidayRepository Holiday
        {
            get
            {
                if (_Holiday == null) _Holiday = AbstractRepositoryFactory.Create_Holiday();
                return _Holiday;
            }
        }
        
 

  


  IHotFdTypeRepository _HotFdType;
    public IHotFdTypeRepository HotFdType
        {
            get
            {
                if (_HotFdType == null) _HotFdType = AbstractRepositoryFactory.Create_HotFdType();
                return _HotFdType;
            }
        }
        
 

  


  IHotFoodRepository _HotFood;
    public IHotFoodRepository HotFood
        {
            get
            {
                if (_HotFood == null) _HotFood = AbstractRepositoryFactory.Create_HotFood();
                return _HotFood;
            }
        }
        
 

  


  IInv_TimeSectionRepository _Inv_TimeSection;
    public IInv_TimeSectionRepository Inv_TimeSection
        {
            get
            {
                if (_Inv_TimeSection == null) _Inv_TimeSection = AbstractRepositoryFactory.Create_Inv_TimeSection();
                return _Inv_TimeSection;
            }
        }
        
 

  


  IInvRollBackRepository _InvRollBack;
    public IInvRollBackRepository InvRollBack
        {
            get
            {
                if (_InvRollBack == null) _InvRollBack = AbstractRepositoryFactory.Create_InvRollBack();
                return _InvRollBack;
            }
        }
        
 

  


  ILanIdRepository _LanId;
    public ILanIdRepository LanId
        {
            get
            {
                if (_LanId == null) _LanId = AbstractRepositoryFactory.Create_LanId();
                return _LanId;
            }
        }
        
 

  


  ILanStringRepository _LanString;
    public ILanStringRepository LanString
        {
            get
            {
                if (_LanString == null) _LanString = AbstractRepositoryFactory.Create_LanString();
                return _LanString;
            }
        }
        
 

  


  ILastInvNoRepository _LastInvNo;
    public ILastInvNoRepository LastInvNo
        {
            get
            {
                if (_LastInvNo == null) _LastInvNo = AbstractRepositoryFactory.Create_LastInvNo();
                return _LastInvNo;
            }
        }
        
 

  


  ILastRefNoRepository _LastRefNo;
    public ILastRefNoRepository LastRefNo
        {
            get
            {
                if (_LastRefNo == null) _LastRefNo = AbstractRepositoryFactory.Create_LastRefNo();
                return _LastRefNo;
            }
        }
        
 

  


  Imeal_distribution_infoRepository _meal_distribution_info;
    public Imeal_distribution_infoRepository meal_distribution_info
        {
            get
            {
                if (_meal_distribution_info == null) _meal_distribution_info = AbstractRepositoryFactory.Create_meal_distribution_info();
                return _meal_distribution_info;
            }
        }
        
 

  


  Imeal_infoRepository _meal_info;
    public Imeal_infoRepository meal_info
        {
            get
            {
                if (_meal_info == null) _meal_info = AbstractRepositoryFactory.Create_meal_info();
                return _meal_info;
            }
        }
        
 

  


  IMembAmountEditLogRepository _MembAmountEditLog;
    public IMembAmountEditLogRepository MembAmountEditLog
        {
            get
            {
                if (_MembAmountEditLog == null) _MembAmountEditLog = AbstractRepositoryFactory.Create_MembAmountEditLog();
                return _MembAmountEditLog;
            }
        }
        
 

  


  IMemberRepository _Member;
    public IMemberRepository Member
        {
            get
            {
                if (_Member == null) _Member = AbstractRepositoryFactory.Create_Member();
                return _Member;
            }
        }
        
 

  


  IMemberCheckoutInfoRepository _MemberCheckoutInfo;
    public IMemberCheckoutInfoRepository MemberCheckoutInfo
        {
            get
            {
                if (_MemberCheckoutInfo == null) _MemberCheckoutInfo = AbstractRepositoryFactory.Create_MemberCheckoutInfo();
                return _MemberCheckoutInfo;
            }
        }
        
 

  


  IMemberGiveSetRepository _MemberGiveSet;
    public IMemberGiveSetRepository MemberGiveSet
        {
            get
            {
                if (_MemberGiveSet == null) _MemberGiveSet = AbstractRepositoryFactory.Create_MemberGiveSet();
                return _MemberGiveSet;
            }
        }
        
 

  


  IMembSetRepository _MembSet;
    public IMembSetRepository MembSet
        {
            get
            {
                if (_MembSet == null) _MembSet = AbstractRepositoryFactory.Create_MembSet();
                return _MembSet;
            }
        }
        
 

  


  IMGradeFdDiscRepository _MGradeFdDisc;
    public IMGradeFdDiscRepository MGradeFdDisc
        {
            get
            {
                if (_MGradeFdDisc == null) _MGradeFdDisc = AbstractRepositoryFactory.Create_MGradeFdDisc();
                return _MGradeFdDisc;
            }
        }
        
 

  


  IMobileFdGiveRepository _MobileFdGive;
    public IMobileFdGiveRepository MobileFdGive
        {
            get
            {
                if (_MobileFdGive == null) _MobileFdGive = AbstractRepositoryFactory.Create_MobileFdGive();
                return _MobileFdGive;
            }
        }
        
 

  


  IMobileFoodRepository _MobileFood;
    public IMobileFoodRepository MobileFood
        {
            get
            {
                if (_MobileFood == null) _MobileFood = AbstractRepositoryFactory.Create_MobileFood();
                return _MobileFood;
            }
        }
        
 

  


  IMobileFoodDiscRepository _MobileFoodDisc;
    public IMobileFoodDiscRepository MobileFoodDisc
        {
            get
            {
                if (_MobileFoodDisc == null) _MobileFoodDisc = AbstractRepositoryFactory.Create_MobileFoodDisc();
                return _MobileFoodDisc;
            }
        }
        
 

  


  IMobileFtTypeRepository _MobileFtType;
    public IMobileFtTypeRepository MobileFtType
        {
            get
            {
                if (_MobileFtType == null) _MobileFtType = AbstractRepositoryFactory.Create_MobileFtType();
                return _MobileFtType;
            }
        }
        
 

  


  IMobilePackGiveRepository _MobilePackGive;
    public IMobilePackGiveRepository MobilePackGive
        {
            get
            {
                if (_MobilePackGive == null) _MobilePackGive = AbstractRepositoryFactory.Create_MobilePackGive();
                return _MobilePackGive;
            }
        }
        
 

  


  IMobilOrderItemRepository _MobilOrderItem;
    public IMobilOrderItemRepository MobilOrderItem
        {
            get
            {
                if (_MobilOrderItem == null) _MobilOrderItem = AbstractRepositoryFactory.Create_MobilOrderItem();
                return _MobilOrderItem;
            }
        }
        
 

  


  IMobilOrderTitleRepository _MobilOrderTitle;
    public IMobilOrderTitleRepository MobilOrderTitle
        {
            get
            {
                if (_MobilOrderTitle == null) _MobilOrderTitle = AbstractRepositoryFactory.Create_MobilOrderTitle();
                return _MobilOrderTitle;
            }
        }
        
 

  


  IMobilUserOrderTitleRepository _MobilUserOrderTitle;
    public IMobilUserOrderTitleRepository MobilUserOrderTitle
        {
            get
            {
                if (_MobilUserOrderTitle == null) _MobilUserOrderTitle = AbstractRepositoryFactory.Create_MobilUserOrderTitle();
                return _MobilUserOrderTitle;
            }
        }
        
 

  


  INewMemberLogRepository _NewMemberLog;
    public INewMemberLogRepository NewMemberLog
        {
            get
            {
                if (_NewMemberLog == null) _NewMemberLog = AbstractRepositoryFactory.Create_NewMemberLog();
                return _NewMemberLog;
            }
        }
        
 

  


  IParamSetRepository _ParamSet;
    public IParamSetRepository ParamSet
        {
            get
            {
                if (_ParamSet == null) _ParamSet = AbstractRepositoryFactory.Create_ParamSet();
                return _ParamSet;
            }
        }
        
 

  


  Ipre_orderRepository _pre_order;
    public Ipre_orderRepository pre_order
        {
            get
            {
                if (_pre_order == null) _pre_order = AbstractRepositoryFactory.Create_pre_order();
                return _pre_order;
            }
        }
        
 

  


  IPreOrderSendMsgInfoRepository _PreOrderSendMsgInfo;
    public IPreOrderSendMsgInfoRepository PreOrderSendMsgInfo
        {
            get
            {
                if (_PreOrderSendMsgInfo == null) _PreOrderSendMsgInfo = AbstractRepositoryFactory.Create_PreOrderSendMsgInfo();
                return _PreOrderSendMsgInfo;
            }
        }
        
 

  


  IPriceNoRepository _PriceNo;
    public IPriceNoRepository PriceNo
        {
            get
            {
                if (_PriceNo == null) _PriceNo = AbstractRepositoryFactory.Create_PriceNo();
                return _PriceNo;
            }
        }
        
 

  


  IQrInfoRepository _QrInfo;
    public IQrInfoRepository QrInfo
        {
            get
            {
                if (_QrInfo == null) _QrInfo = AbstractRepositoryFactory.Create_QrInfo();
                return _QrInfo;
            }
        }
        
 

  


  IRecordRoomTimeRepository _RecordRoomTime;
    public IRecordRoomTimeRepository RecordRoomTime
        {
            get
            {
                if (_RecordRoomTime == null) _RecordRoomTime = AbstractRepositoryFactory.Create_RecordRoomTime();
                return _RecordRoomTime;
            }
        }
        
 

  


  IRefToZDLogRepository _RefToZDLog;
    public IRefToZDLogRepository RefToZDLog
        {
            get
            {
                if (_RefToZDLog == null) _RefToZDLog = AbstractRepositoryFactory.Create_RefToZDLog();
                return _RefToZDLog;
            }
        }
        
 

  


  IRightSetRepository _RightSet;
    public IRightSetRepository RightSet
        {
            get
            {
                if (_RightSet == null) _RightSet = AbstractRepositoryFactory.Create_RightSet();
                return _RightSet;
            }
        }
        
 

  


  IRmAccountInfoRepository _RmAccountInfo;
    public IRmAccountInfoRepository RmAccountInfo
        {
            get
            {
                if (_RmAccountInfo == null) _RmAccountInfo = AbstractRepositoryFactory.Create_RmAccountInfo();
                return _RmAccountInfo;
            }
        }
        
 

  


  IRmAreaRepository _RmArea;
    public IRmAreaRepository RmArea
        {
            get
            {
                if (_RmArea == null) _RmArea = AbstractRepositoryFactory.Create_RmArea();
                return _RmArea;
            }
        }
        
 

  


  IRmClearLogRepository _RmClearLog;
    public IRmClearLogRepository RmClearLog
        {
            get
            {
                if (_RmClearLog == null) _RmClearLog = AbstractRepositoryFactory.Create_RmClearLog();
                return _RmClearLog;
            }
        }
        
 

  


  IRmCloseInfoRepository _RmCloseInfo;
    public IRmCloseInfoRepository RmCloseInfo
        {
            get
            {
                if (_RmCloseInfo == null) _RmCloseInfo = AbstractRepositoryFactory.Create_RmCloseInfo();
                return _RmCloseInfo;
            }
        }
        
 

  


  IRmCloseInfo_CollectRepository _RmCloseInfo_Collect;
    public IRmCloseInfo_CollectRepository RmCloseInfo_Collect
        {
            get
            {
                if (_RmCloseInfo_Collect == null) _RmCloseInfo_Collect = AbstractRepositoryFactory.Create_RmCloseInfo_Collect();
                return _RmCloseInfo_Collect;
            }
        }
        
 

  


  IRmExchangeDetailRepository _RmExchangeDetail;
    public IRmExchangeDetailRepository RmExchangeDetail
        {
            get
            {
                if (_RmExchangeDetail == null) _RmExchangeDetail = AbstractRepositoryFactory.Create_RmExchangeDetail();
                return _RmExchangeDetail;
            }
        }
        
 

  


  IRmExchangeLogRepository _RmExchangeLog;
    public IRmExchangeLogRepository RmExchangeLog
        {
            get
            {
                if (_RmExchangeLog == null) _RmExchangeLog = AbstractRepositoryFactory.Create_RmExchangeLog();
                return _RmExchangeLog;
            }
        }
        
 

  


  IRmFtPrnIndexRepository _RmFtPrnIndex;
    public IRmFtPrnIndexRepository RmFtPrnIndex
        {
            get
            {
                if (_RmFtPrnIndex == null) _RmFtPrnIndex = AbstractRepositoryFactory.Create_RmFtPrnIndex();
                return _RmFtPrnIndex;
            }
        }
        
 

  


  IRmOrderRepository _RmOrder;
    public IRmOrderRepository RmOrder
        {
            get
            {
                if (_RmOrder == null) _RmOrder = AbstractRepositoryFactory.Create_RmOrder();
                return _RmOrder;
            }
        }
        
 

  


  IRmOrderDelLogRepository _RmOrderDelLog;
    public IRmOrderDelLogRepository RmOrderDelLog
        {
            get
            {
                if (_RmOrderDelLog == null) _RmOrderDelLog = AbstractRepositoryFactory.Create_RmOrderDelLog();
                return _RmOrderDelLog;
            }
        }
        
 

  


  IRmOrderLogRepository _RmOrderLog;
    public IRmOrderLogRepository RmOrderLog
        {
            get
            {
                if (_RmOrderLog == null) _RmOrderLog = AbstractRepositoryFactory.Create_RmOrderLog();
                return _RmOrderLog;
            }
        }
        
 

  


  IRmsRoomRepository _RmsRoom;
    public IRmsRoomRepository RmsRoom
        {
            get
            {
                if (_RmsRoom == null) _RmsRoom = AbstractRepositoryFactory.Create_RmsRoom();
                return _RmsRoom;
            }
        }
        
 

  


  IRmTypeRepository _RmType;
    public IRmTypeRepository RmType
        {
            get
            {
                if (_RmType == null) _RmType = AbstractRepositoryFactory.Create_RmType();
                return _RmType;
            }
        }
        
 

  


  IRoomRepository _Room;
    public IRoomRepository Room
        {
            get
            {
                if (_Room == null) _Room = AbstractRepositoryFactory.Create_Room();
                return _Room;
            }
        }
        
 

  


  IRoomCommissionRepository _RoomCommission;
    public IRoomCommissionRepository RoomCommission
        {
            get
            {
                if (_RoomCommission == null) _RoomCommission = AbstractRepositoryFactory.Create_RoomCommission();
                return _RoomCommission;
            }
        }
        
 

  


  IRoomExtendRepository _RoomExtend;
    public IRoomExtendRepository RoomExtend
        {
            get
            {
                if (_RoomExtend == null) _RoomExtend = AbstractRepositoryFactory.Create_RoomExtend();
                return _RoomExtend;
            }
        }
        
 

  


  IRoomtestRepository _Roomtest;
    public IRoomtestRepository Roomtest
        {
            get
            {
                if (_Roomtest == null) _Roomtest = AbstractRepositoryFactory.Create_Roomtest();
                return _Roomtest;
            }
        }
        
 

  


  IRtAutoRepository _RtAuto;
    public IRtAutoRepository RtAuto
        {
            get
            {
                if (_RtAuto == null) _RtAuto = AbstractRepositoryFactory.Create_RtAuto();
                return _RtAuto;
            }
        }
        
 

  


  IRtAutoZDRepository _RtAutoZD;
    public IRtAutoZDRepository RtAutoZD
        {
            get
            {
                if (_RtAutoZD == null) _RtAutoZD = AbstractRepositoryFactory.Create_RtAutoZD();
                return _RtAutoZD;
            }
        }
        
 

  


  IRtTimePriceRepository _RtTimePrice;
    public IRtTimePriceRepository RtTimePrice
        {
            get
            {
                if (_RtTimePrice == null) _RtTimePrice = AbstractRepositoryFactory.Create_RtTimePrice();
                return _RtTimePrice;
            }
        }
        
 

  


  IS_AccTypeRepository _S_AccType;
    public IS_AccTypeRepository S_AccType
        {
            get
            {
                if (_S_AccType == null) _S_AccType = AbstractRepositoryFactory.Create_S_AccType();
                return _S_AccType;
            }
        }
        
 

  


  IS_CashItemRepository _S_CashItem;
    public IS_CashItemRepository S_CashItem
        {
            get
            {
                if (_S_CashItem == null) _S_CashItem = AbstractRepositoryFactory.Create_S_CashItem();
                return _S_CashItem;
            }
        }
        
 

  


  IS_PrnTypeRepository _S_PrnType;
    public IS_PrnTypeRepository S_PrnType
        {
            get
            {
                if (_S_PrnType == null) _S_PrnType = AbstractRepositoryFactory.Create_S_PrnType();
                return _S_PrnType;
            }
        }
        
 

  


  IS_RmStatusRepository _S_RmStatus;
    public IS_RmStatusRepository S_RmStatus
        {
            get
            {
                if (_S_RmStatus == null) _S_RmStatus = AbstractRepositoryFactory.Create_S_RmStatus();
                return _S_RmStatus;
            }
        }
        
 

  


  ISchedulingRecordRepository _SchedulingRecord;
    public ISchedulingRecordRepository SchedulingRecord
        {
            get
            {
                if (_SchedulingRecord == null) _SchedulingRecord = AbstractRepositoryFactory.Create_SchedulingRecord();
                return _SchedulingRecord;
            }
        }
        
 

  


  ISDateRepository _SDate;
    public ISDateRepository SDate
        {
            get
            {
                if (_SDate == null) _SDate = AbstractRepositoryFactory.Create_SDate();
                return _SDate;
            }
        }
        
 

  


  IShareSetInfoRepository _ShareSetInfo;
    public IShareSetInfoRepository ShareSetInfo
        {
            get
            {
                if (_ShareSetInfo == null) _ShareSetInfo = AbstractRepositoryFactory.Create_ShareSetInfo();
                return _ShareSetInfo;
            }
        }
        
 

  


  IShiftInfoRepository _ShiftInfo;
    public IShiftInfoRepository ShiftInfo
        {
            get
            {
                if (_ShiftInfo == null) _ShiftInfo = AbstractRepositoryFactory.Create_ShiftInfo();
                return _ShiftInfo;
            }
        }
        
 

  


  IStarInfoRepository _StarInfo;
    public IStarInfoRepository StarInfo
        {
            get
            {
                if (_StarInfo == null) _StarInfo = AbstractRepositoryFactory.Create_StarInfo();
                return _StarInfo;
            }
        }
        
 

  


  ITestTableRepository _TestTable;
    public ITestTableRepository TestTable
        {
            get
            {
                if (_TestTable == null) _TestTable = AbstractRepositoryFactory.Create_TestTable();
                return _TestTable;
            }
        }
        
 

  


  ITh_RoomCommissionAllotRepository _Th_RoomCommissionAllot;
    public ITh_RoomCommissionAllotRepository Th_RoomCommissionAllot
        {
            get
            {
                if (_Th_RoomCommissionAllot == null) _Th_RoomCommissionAllot = AbstractRepositoryFactory.Create_Th_RoomCommissionAllot();
                return _Th_RoomCommissionAllot;
            }
        }
        
 

  


  ITimeZoneRepository _TimeZone;
    public ITimeZoneRepository TimeZone
        {
            get
            {
                if (_TimeZone == null) _TimeZone = AbstractRepositoryFactory.Create_TimeZone();
                return _TimeZone;
            }
        }
        
 

  


  ItriggerRecordRepository _triggerRecord;
    public ItriggerRecordRepository triggerRecord
        {
            get
            {
                if (_triggerRecord == null) _triggerRecord = AbstractRepositoryFactory.Create_triggerRecord();
                return _triggerRecord;
            }
        }
        
 

  


  IUserAmountRepository _UserAmount;
    public IUserAmountRepository UserAmount
        {
            get
            {
                if (_UserAmount == null) _UserAmount = AbstractRepositoryFactory.Create_UserAmount();
                return _UserAmount;
            }
        }
        
 

  


  IUserAmountDetailRepository _UserAmountDetail;
    public IUserAmountDetailRepository UserAmountDetail
        {
            get
            {
                if (_UserAmountDetail == null) _UserAmountDetail = AbstractRepositoryFactory.Create_UserAmountDetail();
                return _UserAmountDetail;
            }
        }
        
 

  


  IUserFtZDRepository _UserFtZD;
    public IUserFtZDRepository UserFtZD
        {
            get
            {
                if (_UserFtZD == null) _UserFtZD = AbstractRepositoryFactory.Create_UserFtZD();
                return _UserFtZD;
            }
        }
        
 

  


  IUserIORepository _UserIO;
    public IUserIORepository UserIO
        {
            get
            {
                if (_UserIO == null) _UserIO = AbstractRepositoryFactory.Create_UserIO();
                return _UserIO;
            }
        }
        
 

  


  IUserZDItemRepository _UserZDItem;
    public IUserZDItemRepository UserZDItem
        {
            get
            {
                if (_UserZDItem == null) _UserZDItem = AbstractRepositoryFactory.Create_UserZDItem();
                return _UserZDItem;
            }
        }
        
 

  


  IUserZDItemDetailRepository _UserZDItemDetail;
    public IUserZDItemDetailRepository UserZDItemDetail
        {
            get
            {
                if (_UserZDItemDetail == null) _UserZDItemDetail = AbstractRepositoryFactory.Create_UserZDItemDetail();
                return _UserZDItemDetail;
            }
        }
        
 

  


  IUserZDSetRepository _UserZDSet;
    public IUserZDSetRepository UserZDSet
        {
            get
            {
                if (_UserZDSet == null) _UserZDSet = AbstractRepositoryFactory.Create_UserZDSet();
                return _UserZDSet;
            }
        }
        
 

  


  IVesaRepository _Vesa;
    public IVesaRepository Vesa
        {
            get
            {
                if (_Vesa == null) _Vesa = AbstractRepositoryFactory.Create_Vesa();
                return _Vesa;
            }
        }
        
 

  


  IWebOrderTableRepository _WebOrderTable;
    public IWebOrderTableRepository WebOrderTable
        {
            get
            {
                if (_WebOrderTable == null) _WebOrderTable = AbstractRepositoryFactory.Create_WebOrderTable();
                return _WebOrderTable;
            }
        }
        
 

  


  IWeChatFoodOrderMsgRepository _WeChatFoodOrderMsg;
    public IWeChatFoodOrderMsgRepository WeChatFoodOrderMsg
        {
            get
            {
                if (_WeChatFoodOrderMsg == null) _WeChatFoodOrderMsg = AbstractRepositoryFactory.Create_WeChatFoodOrderMsg();
                return _WeChatFoodOrderMsg;
            }
        }
        
 

  


  IWeChatFoodOrderMsg2Repository _WeChatFoodOrderMsg2;
    public IWeChatFoodOrderMsg2Repository WeChatFoodOrderMsg2
        {
            get
            {
                if (_WeChatFoodOrderMsg2 == null) _WeChatFoodOrderMsg2 = AbstractRepositoryFactory.Create_WeChatFoodOrderMsg2();
                return _WeChatFoodOrderMsg2;
            }
        }
        
 

  


  IWindTicketRepository _WindTicket;
    public IWindTicketRepository WindTicket
        {
            get
            {
                if (_WindTicket == null) _WindTicket = AbstractRepositoryFactory.Create_WindTicket();
                return _WindTicket;
            }
        }
        
 

  


  Iwx_shopmall_worktimeRepository _wx_shopmall_worktime;
    public Iwx_shopmall_worktimeRepository wx_shopmall_worktime
        {
            get
            {
                if (_wx_shopmall_worktime == null) _wx_shopmall_worktime = AbstractRepositoryFactory.Create_wx_shopmall_worktime();
                return _wx_shopmall_worktime;
            }
        }
        
 

  


  IwxPayCheckInfoRepository _wxPayCheckInfo;
    public IwxPayCheckInfoRepository wxPayCheckInfo
        {
            get
            {
                if (_wxPayCheckInfo == null) _wxPayCheckInfo = AbstractRepositoryFactory.Create_wxPayCheckInfo();
                return _wxPayCheckInfo;
            }
        }
        
 

  


  IwxPayInfoRepository _wxPayInfo;
    public IwxPayInfoRepository wxPayInfo
        {
            get
            {
                if (_wxPayInfo == null) _wxPayInfo = AbstractRepositoryFactory.Create_wxPayInfo();
                return _wxPayInfo;
            }
        }
        
 

  

 }
}

