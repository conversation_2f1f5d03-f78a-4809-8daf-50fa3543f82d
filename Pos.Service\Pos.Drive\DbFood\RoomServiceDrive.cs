﻿using ComponentApplicationServiceInterface.Context.Response;
using Pos.Model.DbFood.Conntext;
using Pos.Model.DbFood.RespModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Pos.Drive.DbFood
{
    public class RoomServiceDrive : DriveBase
    {
        public ResponseContext<GetRoomInfoModel> GetRoomInfo(GetRoomInfoContext context)
        {
            return ActionFun.Run(context, () =>
            {
                if (string.IsNullOrEmpty(context.RmNo))
                    throw new ExMessage("传入房间号不能为空！");

                return app.Room.GetRoomInfo(context);
            });
        }
    }
}
