//------------------------------------------------------------------------------
// <auto-generated>
//    此代码是根据模板生成的。
//
//    手动更改此文件可能会导致应用程序中发生异常行为。
//    如果重新生成代码，则将覆盖对此文件的手动更改。
// </auto-generated>
//------------------------------------------------------------------------------

namespace Pos.Model.DbContext
{
    using System;
    using System.Collections.Generic;
    
    public partial class Food
    {
        public Food()
        {
            this.FdTimePrice = new HashSet<FdTimePrice>();
            this.FoodCal = new HashSet<FoodCal>();
            this.HotFood = new HashSet<HotFood>();
            this.MGradeFdDisc = new HashSet<MGradeFdDisc>();
            this.RtAuto = new HashSet<RtAuto>();
            this.RtAutoZD = new HashSet<RtAutoZD>();
            this.UserZDItemDetail = new HashSet<UserZDItemDetail>();
        }
    
        public string FdNo { get; set; }
        public string FtNo { get; set; }
        public string FdCName { get; set; }
        public string FdEName { get; set; }
        public Nullable<short> FdQty { get; set; }
        public bool FdLack { get; set; }
        public int FdPrice1 { get; set; }
        public int FdPrice2 { get; set; }
        public int FdPrice3 { get; set; }
        public int FdPrice4 { get; set; }
        public int FdPrice5 { get; set; }
        public int FdPrice6 { get; set; }
        public int FdPrice7 { get; set; }
        public int FdPrice8 { get; set; }
        public int FdPrice9 { get; set; }
        public int FdPrice10 { get; set; }
        public int FdSPrice1 { get; set; }
        public int FdSPrice2 { get; set; }
        public int FdSPrice3 { get; set; }
        public int FdSPrice4 { get; set; }
        public int FdSPrice5 { get; set; }
        public int FdSPrice6 { get; set; }
        public int FdSPrice7 { get; set; }
        public int FdSPrice8 { get; set; }
        public int FdSPrice9 { get; set; }
        public int FdSPrice10 { get; set; }
        public int FdMemberPrice1 { get; set; }
        public int FdMemberPrice2 { get; set; }
        public int FdMemberPrice3 { get; set; }
        public int FdMemberPrice4 { get; set; }
        public int FdMemberPrice5 { get; set; }
        public int FdMemberPrice6 { get; set; }
        public int FdMemberPrice7 { get; set; }
        public int FdMemberPrice8 { get; set; }
        public int FdMemberPrice9 { get; set; }
        public int FdMemberPrice10 { get; set; }
        public bool InRmCost1 { get; set; }
        public bool InRmCost2 { get; set; }
        public bool InRmCost3 { get; set; }
        public bool InRmCost4 { get; set; }
        public bool InRmCost5 { get; set; }
        public bool InRmCost6 { get; set; }
        public bool InRmCost7 { get; set; }
        public bool InRmCost8 { get; set; }
        public bool InRmCost9 { get; set; }
        public bool InRmCost10 { get; set; }
        public bool ChangePrice1 { get; set; }
        public bool ChangePrice2 { get; set; }
        public bool ChangePrice3 { get; set; }
        public bool ChangePrice4 { get; set; }
        public bool ChangePrice5 { get; set; }
        public bool ChangePrice6 { get; set; }
        public bool ChangePrice7 { get; set; }
        public bool ChangePrice8 { get; set; }
        public bool ChangePrice9 { get; set; }
        public bool ChangePrice10 { get; set; }
        public int MiniDisc1 { get; set; }
        public int MiniDisc2 { get; set; }
        public int MiniDisc3 { get; set; }
        public int MiniDisc4 { get; set; }
        public int MiniDisc5 { get; set; }
        public int MiniDisc6 { get; set; }
        public int MiniDisc7 { get; set; }
        public int MiniDisc8 { get; set; }
        public int MiniDisc9 { get; set; }
        public int MiniDisc10 { get; set; }
        public bool Serv1 { get; set; }
        public bool Serv2 { get; set; }
        public bool Serv3 { get; set; }
        public bool Serv4 { get; set; }
        public bool Serv5 { get; set; }
        public bool Serv6 { get; set; }
        public bool Serv7 { get; set; }
        public bool Serv8 { get; set; }
        public bool Serv9 { get; set; }
        public bool Serv10 { get; set; }
        public bool CanDonate1 { get; set; }
        public bool CanDonate2 { get; set; }
        public bool CanDonate3 { get; set; }
        public bool CanDonate4 { get; set; }
        public bool CanDonate5 { get; set; }
        public bool CanDonate6 { get; set; }
        public bool CanDonate7 { get; set; }
        public bool CanDonate8 { get; set; }
        public bool CanDonate9 { get; set; }
        public bool CanDonate10 { get; set; }
        public System.Guid msrepl_tran_version { get; set; }
        public int CommissionPrice { get; set; }
        public string Specs { get; set; }
        public int Discount { get; set; }
        public string GiveGifts { get; set; }
        public int FoodSetType { get; set; }
        public int Snack { get; set; }
        public int SoftDrinks { get; set; }
        public int MixedDrinks { get; set; }
        public string iPadFtNo { get; set; }
        public string iPadUnit { get; set; }
    
        public virtual ICollection<FdTimePrice> FdTimePrice { get; set; }
        public virtual FdType FdType { get; set; }
        public virtual ICollection<FoodCal> FoodCal { get; set; }
        public virtual ICollection<HotFood> HotFood { get; set; }
        public virtual ICollection<MGradeFdDisc> MGradeFdDisc { get; set; }
        public virtual ICollection<RtAuto> RtAuto { get; set; }
        public virtual ICollection<RtAutoZD> RtAutoZD { get; set; }
        public virtual ICollection<UserZDItemDetail> UserZDItemDetail { get; set; }
    }
}
