﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <configSections>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>
  <connectionStrings>
    <add name="dbfoodEntities" connectionString="metadata=res://*/DbContext.FoodDB.csdl|res://*/DbContext.FoodDB.ssdl|res://*/DbContext.FoodDB.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=************;initial catalog=dbfood;user id=sa;password=***;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
  </connectionStrings>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="v11.0" />
      </parameters>
    </defaultConnectionFactory>
  </entityFramework>
	<system.web>
		<compilation debug="true" />
	</system.web>

	<!-- 部署服务库项目时，必须将配置文件的内容添加到
 主机的 app.config 文件中。System.Configuration 不支持库的配置文件。 -->
	<system.serviceModel>
		<bindings>
			<basicHttpBinding>
				<binding name="PosServiceHttpBinding" maxReceivedMessageSize="104857600"></binding>
			</basicHttpBinding>
		</bindings>
		<services>
			<service name="Pos.Service.PosService">
				<host>
					<baseAddresses>
						<add baseAddress = "http://127.0.0.1:9108/PosService/" />
					</baseAddresses>
				</host>
				<!-- Service Endpoints -->
				<!-- 除非完全限定，否则地址将与上面提供的基址相关 -->
				<endpoint address="" binding="basicHttpBinding" contract="Pos.Service.IPosService">
					<!-- 
              部署时，应删除或替换下列标识元素，以反映
             用来运行所部署服务的标识。删除之后，WCF 将
              自动推断相应标识。
          -->
					<identity>
						<dns value="localhost"/>
					</identity>
				</endpoint>
				<!-- Metadata Endpoints -->
				<!-- 元数据交换终结点供相应的服务用于向客户端做自我介绍。 -->
				<!-- 此终结点不使用安全绑定，应在部署前确保其安全或将其删除 -->
				<endpoint address="mex" binding="mexHttpBinding" contract="IMetadataExchange"/>
			</service>
		</services>
		<behaviors>
			<serviceBehaviors>
				<behavior >
					<!-- 为避免泄漏元数据信息，
          请在部署前将以下值设置为 false -->
					<serviceMetadata httpGetEnabled="True"/>
					<!-- 要接收故障异常详细信息以进行调试，
          请将以下值设置为 true。在部署前设置为 false 
          以避免泄漏异常信息 -->
					<serviceDebug includeExceptionDetailInFaults="False" />
				</behavior>
			</serviceBehaviors>
		</behaviors>
	</system.serviceModel>
	
</configuration>