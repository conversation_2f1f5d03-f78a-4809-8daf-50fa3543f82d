//------------------------------------------------------------------------------
// <auto-generated>
//    此代码是根据模板生成的。
//
//    手动更改此文件可能会导致应用程序中发生异常行为。
//    如果重新生成代码，则将覆盖对此文件的手动更改。
// </auto-generated>
//------------------------------------------------------------------------------

namespace Pos.Model.DbContext
{
    using System;
    using System.Collections.Generic;
    
    public partial class MembSet
    {
        public MembSet()
        {
            this.Member = new HashSet<Member>();
            this.MGradeFdDisc = new HashSet<MGradeFdDisc>();
        }
    
        public string MGradeID { get; set; }
        public string MGradeName { get; set; }
        public int MDiscRate { get; set; }
        public bool MNoMinCost { get; set; }
        public bool MNoServ { get; set; }
        public Nullable<int> GZLimit { get; set; }
        public int ValidDays { get; set; }
        public int ScoreUnit { get; set; }
        public string AppendUserId { get; set; }
        public string AppendUserName { get; set; }
        public Nullable<System.DateTime> AppendTime { get; set; }
        public string EditUserId { get; set; }
        public string EditUserName { get; set; }
        public Nullable<System.DateTime> EditTime { get; set; }
        public System.Guid msrepl_tran_version { get; set; }
    
        public virtual ICollection<Member> Member { get; set; }
        public virtual ICollection<MGradeFdDisc> MGradeFdDisc { get; set; }
    }
}
