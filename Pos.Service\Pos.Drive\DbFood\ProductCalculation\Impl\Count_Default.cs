﻿using Pos.Model.DbContext;
using Pos.Model.DbFood.RespModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Pos.Drive.DbFood.ProductCalculation.Impl
{
    /// <summary>
    /// 默认计算规则实现
    /// </summary>
    public class Count_Default : ProductCalculationBase
    {
        public override List<UnifiedComputeModel> UnifiedCompute(List<StoreReportModel> storeData, List<FoodLabel> packages)
        {
            return new List<UnifiedComputeModel>();
        }
    }
}
