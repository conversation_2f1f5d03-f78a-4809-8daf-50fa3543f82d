﻿using Pos.Model.DbContext;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Pos.Model.DbFood.Conntext
{
    public class FoodLabelContext
    {
        /// <summary>
        /// 商品类别
        /// </summary>
        public string FtNo { get; set; }

        /// <summary>
        /// 商品名称或者商品编号
        /// </summary>
        public string SearchKey { get; set; }

        public ComponentApplicationServiceInterface.Web.Pagination pagination { get; set; }

        /// <summary>
        /// 是否存在有label标签信息
        /// </summary>
        public Nullable<bool> HasLabel { get; set; }
    }

    public class BatchImportContext : FoodLabel
    {
        public Nullable<int> TypeId { get; set; }
        public Nullable<int> CateId { get; set; }
    }

    public class GetFoodLabelInfoContext
    {
        public string FoodCode { get; set; }
    }

    public class DeleteFoodLabelContext
    {
        public string FoodCode { get; set; }
    }

    public class GetFtDropDownContext 
    { }

    public class CreateLabelContext 
    {
        public string FdNo { get; set; }
        public Nullable<int> RoomType { get; set; }
        public Nullable<int> TypeId { get; set; }
        public Nullable<int> PeoNumber { get; set; }
        public string Platform { get; set; }
        public string Depart { get; set; }
        public Nullable<int> BeverageType { get; set; }
        public Nullable<int> SnackType { get; set; }
        public Nullable<int> Unit { get; set; }
        public Nullable<bool> MemberMode { get; set; }
        public Nullable<int> CardType { get; set; }
        public Nullable<int> CashType { get; set; }
        public Nullable<int> HeadType { get; set; }
        public string Period { get; set; }
        public string Bank { get; set; }
        public Nullable<int> VoucherType { get; set; }
        public Nullable<int> MemberCard { get; set; }
        public Nullable<int> MemberLevel { get; set; }
        public Nullable<int> Losses { get; set; }
        public Nullable<int> PriceType { get; set; }
        public Nullable<int> BJF { get; set; }
        public Nullable<int> ServiceCharge { get; set; }
        public Nullable<int> Deduction { get; set; }
        public Nullable<int> Hours { get; set; }
        public Nullable<int> Quantity { get; set; }
        public Nullable<int> Integral { get; set; }
        public Nullable<int> PackageCost { get; set; }
        public Nullable<int> PackageType { get; set; }
        public Nullable<int> GoodsType { get; set; }
        public Nullable<decimal> DivideBar { get; set; }
        public Nullable<decimal> DivideRestaurant { get; set; }
        public Nullable<decimal> DivideInfield { get; set; }
        public Nullable<decimal> DivideBuffet { get; set; }
        public Nullable<int> CateId { get; set; }
        public string Category2 { get; set; }
    }
}
