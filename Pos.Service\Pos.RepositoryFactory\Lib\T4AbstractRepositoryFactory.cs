﻿

using Pos.Domain.IRepository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Reflection;

namespace Pos.RepositoryFactory
{

 public partial class AbstractRepositoryFactory {

   public static IAddItemRepository Create_AddItem()
        {
		
            return (IAddItemRepository)CreateInstance(fullClassName+".AddItemRepository");
        }
   public static IAiTypeRepository Create_AiType()
        {
		
            return (IAiTypeRepository)CreateInstance(fullClassName+".AiTypeRepository");
        }
   public static IAmountLogRepository Create_AmountLog()
        {
		
            return (IAmountLogRepository)CreateInstance(fullClassName+".AmountLogRepository");
        }
   public static ICarLeaveLogRepository Create_CarLeaveLog()
        {
		
            return (ICarLeaveLogRepository)CreateInstance(fullClassName+".CarLeaveLogRepository");
        }
   public static ICashierRepository Create_Cashier()
        {
		
            return (ICashierRepository)CreateInstance(fullClassName+".CashierRepository");
        }
   public static IClearDataLogRepository Create_ClearDataLog()
        {
		
            return (IClearDataLogRepository)CreateInstance(fullClassName+".ClearDataLogRepository");
        }
   public static IDeadLockLogRepository Create_DeadLockLog()
        {
		
            return (IDeadLockLogRepository)CreateInstance(fullClassName+".DeadLockLogRepository");
        }
   public static IDepositInfoRepository Create_DepositInfo()
        {
		
            return (IDepositInfoRepository)CreateInstance(fullClassName+".DepositInfoRepository");
        }
   public static IDeptRepository Create_Dept()
        {
		
            return (IDeptRepository)CreateInstance(fullClassName+".DeptRepository");
        }
   public static IDeptBanSetRepository Create_DeptBanSet()
        {
		
            return (IDeptBanSetRepository)CreateInstance(fullClassName+".DeptBanSetRepository");
        }
   public static IdtpropertiesRepository Create_dtproperties()
        {
		
            return (IdtpropertiesRepository)CreateInstance(fullClassName+".dtpropertiesRepository");
        }
   public static IFdCashRepository Create_FdCash()
        {
		
            return (IFdCashRepository)CreateInstance(fullClassName+".FdCashRepository");
        }
   public static IFdCash_TrackRepository Create_FdCash_Track()
        {
		
            return (IFdCash_TrackRepository)CreateInstance(fullClassName+".FdCash_TrackRepository");
        }
   public static IFdCashBakRepository Create_FdCashBak()
        {
		
            return (IFdCashBakRepository)CreateInstance(fullClassName+".FdCashBakRepository");
        }
   public static IFdCashBak_BRepository Create_FdCashBak_B()
        {
		
            return (IFdCashBak_BRepository)CreateInstance(fullClassName+".FdCashBak_BRepository");
        }
   public static IFdCashBak_BakRepository Create_FdCashBak_Bak()
        {
		
            return (IFdCashBak_BakRepository)CreateInstance(fullClassName+".FdCashBak_BakRepository");
        }
   public static IFdDetTypeRepository Create_FdDetType()
        {
		
            return (IFdDetTypeRepository)CreateInstance(fullClassName+".FdDetTypeRepository");
        }
   public static IFdImageRepository Create_FdImage()
        {
		
            return (IFdImageRepository)CreateInstance(fullClassName+".FdImageRepository");
        }
   public static IFdInvRepository Create_FdInv()
        {
		
            return (IFdInvRepository)CreateInstance(fullClassName+".FdInvRepository");
        }
   public static IFdInv_BRepository Create_FdInv_B()
        {
		
            return (IFdInv_BRepository)CreateInstance(fullClassName+".FdInv_BRepository");
        }
   public static IFdInv_BakRepository Create_FdInv_Bak()
        {
		
            return (IFdInv_BakRepository)CreateInstance(fullClassName+".FdInv_BakRepository");
        }
   public static IFdInv_ExchangeLogRepository Create_FdInv_ExchangeLog()
        {
		
            return (IFdInv_ExchangeLogRepository)CreateInstance(fullClassName+".FdInv_ExchangeLogRepository");
        }
   public static IFdInvCashItemRepository Create_FdInvCashItem()
        {
		
            return (IFdInvCashItemRepository)CreateInstance(fullClassName+".FdInvCashItemRepository");
        }
   public static IFdInvDescRepository Create_FdInvDesc()
        {
		
            return (IFdInvDescRepository)CreateInstance(fullClassName+".FdInvDescRepository");
        }
   public static IFdTicketRepository Create_FdTicket()
        {
		
            return (IFdTicketRepository)CreateInstance(fullClassName+".FdTicketRepository");
        }
   public static IFdTimePriceRepository Create_FdTimePrice()
        {
		
            return (IFdTimePriceRepository)CreateInstance(fullClassName+".FdTimePriceRepository");
        }
   public static IFdTimeZoneRepository Create_FdTimeZone()
        {
		
            return (IFdTimeZoneRepository)CreateInstance(fullClassName+".FdTimeZoneRepository");
        }
   public static IFdTypeRepository Create_FdType()
        {
		
            return (IFdTypeRepository)CreateInstance(fullClassName+".FdTypeRepository");
        }
   public static IFdUserRepository Create_FdUser()
        {
		
            return (IFdUserRepository)CreateInstance(fullClassName+".FdUserRepository");
        }
   public static IFdUserGradeRepository Create_FdUserGrade()
        {
		
            return (IFdUserGradeRepository)CreateInstance(fullClassName+".FdUserGradeRepository");
        }
   public static IFdUserRightsRepository Create_FdUserRights()
        {
		
            return (IFdUserRightsRepository)CreateInstance(fullClassName+".FdUserRightsRepository");
        }
   public static IFestivalTimeRepository Create_FestivalTime()
        {
		
            return (IFestivalTimeRepository)CreateInstance(fullClassName+".FestivalTimeRepository");
        }
   public static IFoodRepository Create_Food()
        {
		
            return (IFoodRepository)CreateInstance(fullClassName+".FoodRepository");
        }
   public static IFoodCalRepository Create_FoodCal()
        {
		
            return (IFoodCalRepository)CreateInstance(fullClassName+".FoodCalRepository");
        }
   public static IFoodLabelRepository Create_FoodLabel()
        {
		
            return (IFoodLabelRepository)CreateInstance(fullClassName+".FoodLabelRepository");
        }
   public static IFoodOrderMRepository Create_FoodOrderM()
        {
		
            return (IFoodOrderMRepository)CreateInstance(fullClassName+".FoodOrderMRepository");
        }
   public static IFPrnRepository Create_FPrn()
        {
		
            return (IFPrnRepository)CreateInstance(fullClassName+".FPrnRepository");
        }
   public static IFPrnDataRepository Create_FPrnData()
        {
		
            return (IFPrnDataRepository)CreateInstance(fullClassName+".FPrnDataRepository");
        }
   public static IFPrnData_BakRepository Create_FPrnData_Bak()
        {
		
            return (IFPrnData_BakRepository)CreateInstance(fullClassName+".FPrnData_BakRepository");
        }
   public static IFreePackageCoupon_RecordRepository Create_FreePackageCoupon_Record()
        {
		
            return (IFreePackageCoupon_RecordRepository)CreateInstance(fullClassName+".FreePackageCoupon_RecordRepository");
        }
   public static IFtInfoRepository Create_FtInfo()
        {
		
            return (IFtInfoRepository)CreateInstance(fullClassName+".FtInfoRepository");
        }
   public static IGDDB20InfoRepository Create_GDDB20Info()
        {
		
            return (IGDDB20InfoRepository)CreateInstance(fullClassName+".GDDB20InfoRepository");
        }
   public static IGDDBInfoRepository Create_GDDBInfo()
        {
		
            return (IGDDBInfoRepository)CreateInstance(fullClassName+".GDDBInfoRepository");
        }
   public static IHappyRabRepository Create_HappyRab()
        {
		
            return (IHappyRabRepository)CreateInstance(fullClassName+".HappyRabRepository");
        }
   public static IHolidayRepository Create_Holiday()
        {
		
            return (IHolidayRepository)CreateInstance(fullClassName+".HolidayRepository");
        }
   public static IHotFdTypeRepository Create_HotFdType()
        {
		
            return (IHotFdTypeRepository)CreateInstance(fullClassName+".HotFdTypeRepository");
        }
   public static IHotFoodRepository Create_HotFood()
        {
		
            return (IHotFoodRepository)CreateInstance(fullClassName+".HotFoodRepository");
        }
   public static IInv_TimeSectionRepository Create_Inv_TimeSection()
        {
		
            return (IInv_TimeSectionRepository)CreateInstance(fullClassName+".Inv_TimeSectionRepository");
        }
   public static IInvRollBackRepository Create_InvRollBack()
        {
		
            return (IInvRollBackRepository)CreateInstance(fullClassName+".InvRollBackRepository");
        }
   public static ILanIdRepository Create_LanId()
        {
		
            return (ILanIdRepository)CreateInstance(fullClassName+".LanIdRepository");
        }
   public static ILanStringRepository Create_LanString()
        {
		
            return (ILanStringRepository)CreateInstance(fullClassName+".LanStringRepository");
        }
   public static ILastInvNoRepository Create_LastInvNo()
        {
		
            return (ILastInvNoRepository)CreateInstance(fullClassName+".LastInvNoRepository");
        }
   public static ILastRefNoRepository Create_LastRefNo()
        {
		
            return (ILastRefNoRepository)CreateInstance(fullClassName+".LastRefNoRepository");
        }
   public static Imeal_distribution_infoRepository Create_meal_distribution_info()
        {
		
            return (Imeal_distribution_infoRepository)CreateInstance(fullClassName+".meal_distribution_infoRepository");
        }
   public static Imeal_infoRepository Create_meal_info()
        {
		
            return (Imeal_infoRepository)CreateInstance(fullClassName+".meal_infoRepository");
        }
   public static IMembAmountEditLogRepository Create_MembAmountEditLog()
        {
		
            return (IMembAmountEditLogRepository)CreateInstance(fullClassName+".MembAmountEditLogRepository");
        }
   public static IMemberRepository Create_Member()
        {
		
            return (IMemberRepository)CreateInstance(fullClassName+".MemberRepository");
        }
   public static IMemberCheckoutInfoRepository Create_MemberCheckoutInfo()
        {
		
            return (IMemberCheckoutInfoRepository)CreateInstance(fullClassName+".MemberCheckoutInfoRepository");
        }
   public static IMemberGiveSetRepository Create_MemberGiveSet()
        {
		
            return (IMemberGiveSetRepository)CreateInstance(fullClassName+".MemberGiveSetRepository");
        }
   public static IMembSetRepository Create_MembSet()
        {
		
            return (IMembSetRepository)CreateInstance(fullClassName+".MembSetRepository");
        }
   public static IMGradeFdDiscRepository Create_MGradeFdDisc()
        {
		
            return (IMGradeFdDiscRepository)CreateInstance(fullClassName+".MGradeFdDiscRepository");
        }
   public static IMobileFdGiveRepository Create_MobileFdGive()
        {
		
            return (IMobileFdGiveRepository)CreateInstance(fullClassName+".MobileFdGiveRepository");
        }
   public static IMobileFoodRepository Create_MobileFood()
        {
		
            return (IMobileFoodRepository)CreateInstance(fullClassName+".MobileFoodRepository");
        }
   public static IMobileFoodDiscRepository Create_MobileFoodDisc()
        {
		
            return (IMobileFoodDiscRepository)CreateInstance(fullClassName+".MobileFoodDiscRepository");
        }
   public static IMobileFtTypeRepository Create_MobileFtType()
        {
		
            return (IMobileFtTypeRepository)CreateInstance(fullClassName+".MobileFtTypeRepository");
        }
   public static IMobilePackGiveRepository Create_MobilePackGive()
        {
		
            return (IMobilePackGiveRepository)CreateInstance(fullClassName+".MobilePackGiveRepository");
        }
   public static IMobilOrderItemRepository Create_MobilOrderItem()
        {
		
            return (IMobilOrderItemRepository)CreateInstance(fullClassName+".MobilOrderItemRepository");
        }
   public static IMobilOrderTitleRepository Create_MobilOrderTitle()
        {
		
            return (IMobilOrderTitleRepository)CreateInstance(fullClassName+".MobilOrderTitleRepository");
        }
   public static IMobilUserOrderTitleRepository Create_MobilUserOrderTitle()
        {
		
            return (IMobilUserOrderTitleRepository)CreateInstance(fullClassName+".MobilUserOrderTitleRepository");
        }
   public static INewMemberLogRepository Create_NewMemberLog()
        {
		
            return (INewMemberLogRepository)CreateInstance(fullClassName+".NewMemberLogRepository");
        }
   public static IParamSetRepository Create_ParamSet()
        {
		
            return (IParamSetRepository)CreateInstance(fullClassName+".ParamSetRepository");
        }
   public static Ipre_orderRepository Create_pre_order()
        {
		
            return (Ipre_orderRepository)CreateInstance(fullClassName+".pre_orderRepository");
        }
   public static IPreOrderSendMsgInfoRepository Create_PreOrderSendMsgInfo()
        {
		
            return (IPreOrderSendMsgInfoRepository)CreateInstance(fullClassName+".PreOrderSendMsgInfoRepository");
        }
   public static IPriceNoRepository Create_PriceNo()
        {
		
            return (IPriceNoRepository)CreateInstance(fullClassName+".PriceNoRepository");
        }
   public static IQrInfoRepository Create_QrInfo()
        {
		
            return (IQrInfoRepository)CreateInstance(fullClassName+".QrInfoRepository");
        }
   public static IRecordRoomTimeRepository Create_RecordRoomTime()
        {
		
            return (IRecordRoomTimeRepository)CreateInstance(fullClassName+".RecordRoomTimeRepository");
        }
   public static IRefToZDLogRepository Create_RefToZDLog()
        {
		
            return (IRefToZDLogRepository)CreateInstance(fullClassName+".RefToZDLogRepository");
        }
   public static IRightSetRepository Create_RightSet()
        {
		
            return (IRightSetRepository)CreateInstance(fullClassName+".RightSetRepository");
        }
   public static IRmAccountInfoRepository Create_RmAccountInfo()
        {
		
            return (IRmAccountInfoRepository)CreateInstance(fullClassName+".RmAccountInfoRepository");
        }
   public static IRmAreaRepository Create_RmArea()
        {
		
            return (IRmAreaRepository)CreateInstance(fullClassName+".RmAreaRepository");
        }
   public static IRmClearLogRepository Create_RmClearLog()
        {
		
            return (IRmClearLogRepository)CreateInstance(fullClassName+".RmClearLogRepository");
        }
   public static IRmCloseInfoRepository Create_RmCloseInfo()
        {
		
            return (IRmCloseInfoRepository)CreateInstance(fullClassName+".RmCloseInfoRepository");
        }
   public static IRmCloseInfo_CollectRepository Create_RmCloseInfo_Collect()
        {
		
            return (IRmCloseInfo_CollectRepository)CreateInstance(fullClassName+".RmCloseInfo_CollectRepository");
        }
   public static IRmExchangeDetailRepository Create_RmExchangeDetail()
        {
		
            return (IRmExchangeDetailRepository)CreateInstance(fullClassName+".RmExchangeDetailRepository");
        }
   public static IRmExchangeLogRepository Create_RmExchangeLog()
        {
		
            return (IRmExchangeLogRepository)CreateInstance(fullClassName+".RmExchangeLogRepository");
        }
   public static IRmFtPrnIndexRepository Create_RmFtPrnIndex()
        {
		
            return (IRmFtPrnIndexRepository)CreateInstance(fullClassName+".RmFtPrnIndexRepository");
        }
   public static IRmOrderRepository Create_RmOrder()
        {
		
            return (IRmOrderRepository)CreateInstance(fullClassName+".RmOrderRepository");
        }
   public static IRmOrderDelLogRepository Create_RmOrderDelLog()
        {
		
            return (IRmOrderDelLogRepository)CreateInstance(fullClassName+".RmOrderDelLogRepository");
        }
   public static IRmOrderLogRepository Create_RmOrderLog()
        {
		
            return (IRmOrderLogRepository)CreateInstance(fullClassName+".RmOrderLogRepository");
        }
   public static IRmsRoomRepository Create_RmsRoom()
        {
		
            return (IRmsRoomRepository)CreateInstance(fullClassName+".RmsRoomRepository");
        }
   public static IRmTypeRepository Create_RmType()
        {
		
            return (IRmTypeRepository)CreateInstance(fullClassName+".RmTypeRepository");
        }
   public static IRoomRepository Create_Room()
        {
		
            return (IRoomRepository)CreateInstance(fullClassName+".RoomRepository");
        }
   public static IRoomCommissionRepository Create_RoomCommission()
        {
		
            return (IRoomCommissionRepository)CreateInstance(fullClassName+".RoomCommissionRepository");
        }
   public static IRoomExtendRepository Create_RoomExtend()
        {
		
            return (IRoomExtendRepository)CreateInstance(fullClassName+".RoomExtendRepository");
        }
   public static IRoomtestRepository Create_Roomtest()
        {
		
            return (IRoomtestRepository)CreateInstance(fullClassName+".RoomtestRepository");
        }
   public static IRtAutoRepository Create_RtAuto()
        {
		
            return (IRtAutoRepository)CreateInstance(fullClassName+".RtAutoRepository");
        }
   public static IRtAutoZDRepository Create_RtAutoZD()
        {
		
            return (IRtAutoZDRepository)CreateInstance(fullClassName+".RtAutoZDRepository");
        }
   public static IRtTimePriceRepository Create_RtTimePrice()
        {
		
            return (IRtTimePriceRepository)CreateInstance(fullClassName+".RtTimePriceRepository");
        }
   public static IS_AccTypeRepository Create_S_AccType()
        {
		
            return (IS_AccTypeRepository)CreateInstance(fullClassName+".S_AccTypeRepository");
        }
   public static IS_CashItemRepository Create_S_CashItem()
        {
		
            return (IS_CashItemRepository)CreateInstance(fullClassName+".S_CashItemRepository");
        }
   public static IS_PrnTypeRepository Create_S_PrnType()
        {
		
            return (IS_PrnTypeRepository)CreateInstance(fullClassName+".S_PrnTypeRepository");
        }
   public static IS_RmStatusRepository Create_S_RmStatus()
        {
		
            return (IS_RmStatusRepository)CreateInstance(fullClassName+".S_RmStatusRepository");
        }
   public static ISchedulingRecordRepository Create_SchedulingRecord()
        {
		
            return (ISchedulingRecordRepository)CreateInstance(fullClassName+".SchedulingRecordRepository");
        }
   public static ISDateRepository Create_SDate()
        {
		
            return (ISDateRepository)CreateInstance(fullClassName+".SDateRepository");
        }
   public static IShareSetInfoRepository Create_ShareSetInfo()
        {
		
            return (IShareSetInfoRepository)CreateInstance(fullClassName+".ShareSetInfoRepository");
        }
   public static IShiftInfoRepository Create_ShiftInfo()
        {
		
            return (IShiftInfoRepository)CreateInstance(fullClassName+".ShiftInfoRepository");
        }
   public static IStarInfoRepository Create_StarInfo()
        {
		
            return (IStarInfoRepository)CreateInstance(fullClassName+".StarInfoRepository");
        }
   public static ITestTableRepository Create_TestTable()
        {
		
            return (ITestTableRepository)CreateInstance(fullClassName+".TestTableRepository");
        }
   public static ITh_RoomCommissionAllotRepository Create_Th_RoomCommissionAllot()
        {
		
            return (ITh_RoomCommissionAllotRepository)CreateInstance(fullClassName+".Th_RoomCommissionAllotRepository");
        }
   public static ITimeZoneRepository Create_TimeZone()
        {
		
            return (ITimeZoneRepository)CreateInstance(fullClassName+".TimeZoneRepository");
        }
   public static ItriggerRecordRepository Create_triggerRecord()
        {
		
            return (ItriggerRecordRepository)CreateInstance(fullClassName+".triggerRecordRepository");
        }
   public static IUserAmountRepository Create_UserAmount()
        {
		
            return (IUserAmountRepository)CreateInstance(fullClassName+".UserAmountRepository");
        }
   public static IUserAmountDetailRepository Create_UserAmountDetail()
        {
		
            return (IUserAmountDetailRepository)CreateInstance(fullClassName+".UserAmountDetailRepository");
        }
   public static IUserFtZDRepository Create_UserFtZD()
        {
		
            return (IUserFtZDRepository)CreateInstance(fullClassName+".UserFtZDRepository");
        }
   public static IUserIORepository Create_UserIO()
        {
		
            return (IUserIORepository)CreateInstance(fullClassName+".UserIORepository");
        }
   public static IUserZDItemRepository Create_UserZDItem()
        {
		
            return (IUserZDItemRepository)CreateInstance(fullClassName+".UserZDItemRepository");
        }
   public static IUserZDItemDetailRepository Create_UserZDItemDetail()
        {
		
            return (IUserZDItemDetailRepository)CreateInstance(fullClassName+".UserZDItemDetailRepository");
        }
   public static IUserZDSetRepository Create_UserZDSet()
        {
		
            return (IUserZDSetRepository)CreateInstance(fullClassName+".UserZDSetRepository");
        }
   public static IVesaRepository Create_Vesa()
        {
		
            return (IVesaRepository)CreateInstance(fullClassName+".VesaRepository");
        }
   public static IWebOrderTableRepository Create_WebOrderTable()
        {
		
            return (IWebOrderTableRepository)CreateInstance(fullClassName+".WebOrderTableRepository");
        }
   public static IWeChatFoodOrderMsgRepository Create_WeChatFoodOrderMsg()
        {
		
            return (IWeChatFoodOrderMsgRepository)CreateInstance(fullClassName+".WeChatFoodOrderMsgRepository");
        }
   public static IWeChatFoodOrderMsg2Repository Create_WeChatFoodOrderMsg2()
        {
		
            return (IWeChatFoodOrderMsg2Repository)CreateInstance(fullClassName+".WeChatFoodOrderMsg2Repository");
        }
   public static IWindTicketRepository Create_WindTicket()
        {
		
            return (IWindTicketRepository)CreateInstance(fullClassName+".WindTicketRepository");
        }
   public static Iwx_shopmall_worktimeRepository Create_wx_shopmall_worktime()
        {
		
            return (Iwx_shopmall_worktimeRepository)CreateInstance(fullClassName+".wx_shopmall_worktimeRepository");
        }
   public static IwxPayCheckInfoRepository Create_wxPayCheckInfo()
        {
		
            return (IwxPayCheckInfoRepository)CreateInstance(fullClassName+".wxPayCheckInfoRepository");
        }
   public static IwxPayInfoRepository Create_wxPayInfo()
        {
		
            return (IwxPayInfoRepository)CreateInstance(fullClassName+".wxPayInfoRepository");
        }
 
  static object CreateInstance(string fullClassName)
        {

            var assembly = Assembly.Load(assemblyName);
            return assembly.CreateInstance(fullClassName);
        }
		static string fullClassName="Pos.Repository";
		static string assemblyName= "Pos.Repository";

 }
  


}
