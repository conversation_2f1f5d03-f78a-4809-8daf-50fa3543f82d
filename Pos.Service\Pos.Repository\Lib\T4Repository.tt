﻿<#@ template debug="false" hostspecific="false" language="C#" #>
<#@ include file="EF.Utility.CS.ttinclude"#>
<#@ output extension=".cs" #>
<#
string   modelNamespace="Pos";
CodeGenerationTools code =new CodeGenerationTools(this);
MetadataLoader Loader =new MetadataLoader(this);
CodeRegion region=new CodeRegion(this,1);
MetadataTools ef=new MetadataTools(this);
string inputFile=modelNamespace+@".Model\DbContext\\FoodDB.edmx";
EdmItemCollection Itemcollection = Loader.CreateEdmItemCollection(inputFile);
string namespaceName=code.VsNamespaceSuggestion();
EntityFrameworkTemplateFileManager fileManager = EntityFrameworkTemplateFileManager.Create(this);
#>

using <#=modelNamespace#>.Domain.IRepository;
using <#=modelNamespace#>.Model.DbContext;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace <#=modelNamespace#>.Repository
{
<#
foreach(EntityType entity in Itemcollection.GetItems<EntityType>().OrderBy(e=>e.Name))
{
#>
 public partial class <#=entity.Name#>Repository : BaseRepository<<#=modelNamespace#>.Model.DbContext.<#=entity.Name#>>,I<#=entity.Name#>Repository  {}
  

<#}#>
}