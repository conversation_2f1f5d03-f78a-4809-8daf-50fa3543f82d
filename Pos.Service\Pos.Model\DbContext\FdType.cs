//------------------------------------------------------------------------------
// <auto-generated>
//    此代码是根据模板生成的。
//
//    手动更改此文件可能会导致应用程序中发生异常行为。
//    如果重新生成代码，则将覆盖对此文件的手动更改。
// </auto-generated>
//------------------------------------------------------------------------------

namespace Pos.Model.DbContext
{
    using System;
    using System.Collections.Generic;
    
    public partial class FdType
    {
        public FdType()
        {
            this.FdDetType = new HashSet<FdDetType>();
            this.Food = new HashSet<Food>();
            this.HotFdType = new HashSet<HotFdType>();
            this.RmFtPrnIndex = new HashSet<RmFtPrnIndex>();
            this.UserFtZD = new HashSet<UserFtZD>();
        }
    
        public string FtNo { get; set; }
        public string FtCName { get; set; }
        public string FtEName { get; set; }
        public string PrnType { get; set; }
        public string FPrnTypeA { get; set; }
        public string FPrnIndexA { get; set; }
        public string DPrnTypeA { get; set; }
        public string DPrnIndexA { get; set; }
        public string FPrnTypeB { get; set; }
        public string FPrnIndexB { get; set; }
        public string DPrnTypeB { get; set; }
        public string DPrnIndexB { get; set; }
        public System.Guid msrepl_tran_version { get; set; }
    
        public virtual ICollection<FdDetType> FdDetType { get; set; }
        public virtual ICollection<Food> Food { get; set; }
        public virtual ICollection<HotFdType> HotFdType { get; set; }
        public virtual ICollection<RmFtPrnIndex> RmFtPrnIndex { get; set; }
        public virtual ICollection<UserFtZD> UserFtZD { get; set; }
    }
}
