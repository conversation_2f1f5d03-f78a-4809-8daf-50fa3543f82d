﻿using Pos.Model.DbContext;
using Pos.Model.DbFood.RespModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Pos.Drive.DbFood.ProductCalculation.Impl
{
    /// <summary>
    /// 商品标签的类型计算实现
    /// </summary>
    public class Count_Type : ProductCalculationBase
    {
        public override List<UnifiedComputeModel> UnifiedCompute(List<StoreReportModel> storeData, List<FoodLabel> lables)
        {
            var packageIds = storeData.Where(w => w.IsPackage > 0).Select(x => x.FdNo).Distinct().ToList();
            var sharePackageList = app.ShareSetInfo.IQueryable(x => packageIds.Contains(x.ShareFdNo)).ToList();

            var resultData = new List<UnifiedComputeModel>();

            storeData.ForEach(w =>
            {
                var data = new UnifiedComputeModel()
                {
                    No = w.FdNo,
                    FtName = w.FtCName,
                    FtNo = w.FtNo,
                    FdQty = w.FdQty,
                    FdPrice = w.FdPrice
                };

                if (w.CashType != "Z")
                {
                    if (w.IsPackage > 0 || (w.IsPackage <= 0 && w.FdCName.Contains("低消")))
                    {
                        //查出固定套餐里面的商品
                        var fdNos = sharePackageList.Where(x => x.ShareFdNo == w.FdNo).Select(x => x.FdNo).ToList();
                        //查出非固定套餐赠送的上商品
                        var giveOrders = storeData.Where(x => !fdNos.Contains(x.FdNo) && x.CashUserName.Contains("赠送") && x.InvNo == w.InvNo).ToList();
                        //查出固定套餐所包含的商品
                        giveOrders = giveOrders.Union(storeData.Where(x => fdNos.Contains(x.FdNo) && x.InvNo == w.InvNo && x.CashUserName.Contains("赠送")).ToList()).ToList();

                        //将套餐里面的所有商品查询出来
                        var fdIds = giveOrders.Select(x => x.FdNo).Distinct().ToList();
                        var lableData = lables.Where(x => fdIds.Contains(x.FdNo)).ToList();

                        var vightIds = lableData.Where(x => x.Type == "厨房").Select(x => x.FdNo).ToList();//餐饮
                        var vightFood = giveOrders.Where(give => vightIds.Contains(give.FdNo)).ToList().Select(give => new DivisionModel()
                        {
                            FdNo = give.FdNo,
                            FdQty = give.FdQty,
                            FdPrice = give.FdPrice
                        }).ToList();

                        var barIds = lableData.Where(x => x.Type == "酒吧").Select(x => x.FdNo).ToList();//酒吧
                        var barFood = giveOrders.Where(give => barIds.Contains(give.FdNo)).ToList().Select(give => new DivisionModel()
                        {
                            FdNo = give.FdNo,
                            FdQty = give.FdQty,
                            FdPrice = give.FdPrice
                        }).ToList();

                        data.Package = new PackageProductModel()
                        {
                            VightFood = vightFood,
                            BarFood = barFood
                        };
                    }
                    else
                    {
                        var lable = lables.FirstOrDefault(x => x.FdNo.Trim() == w.FdNo.Trim());
                        if (lable == null)
                            Common.LogHelper.Error("食品标签：" + w.FdNo + "查出FoodLabel标签为Null，请检查！");

                        data.BarDivision = lable == null ? 0 : lable.DivideBar.Value;
                        data.BuffetDivision = lable == null ? 0 : lable.DivideBuffet.Value;
                        data.NightDivision = lable == null ? 0 : lable.DivideRestaurant.Value;
                        data.InfieldDivision = lable == null ? 0 : lable.DivideInfield.Value;
                    }
                }

                resultData.Add(data);
            });

            return resultData;
        }
    }
}
