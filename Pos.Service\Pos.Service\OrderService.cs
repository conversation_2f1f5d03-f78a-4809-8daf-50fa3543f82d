﻿using ComponentApplicationServiceInterface.Context.Response;
using Pos.Drive.DbFood;
using Pos.Model.DbFood.Conntext;
using Pos.Service.InterfaceService;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Pos.Service
{
    public partial class PosService : IOrderService
    {
        public ResponseContext<bool> PlaceOrder(PlaceOrderContext model)
        {
            return new OrderServiceDrive().PlaceOrder(model);
        }

        public ResponseContext<int> GetOrderNumbers(GetOrderNumbersContext context)
        {
            return new OrderServiceDrive().GetOrderNumbers(context);
        }
    }
}
