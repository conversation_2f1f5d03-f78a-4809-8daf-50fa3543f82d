﻿<#@ template debug="false" hostspecific="false" language="C#" #>
<#@ include file="EF.Utility.CS.ttinclude"#>
<#@ output extension=".cs" #>
<#
string   modelNamespace="Pos";
CodeGenerationTools code =new CodeGenerationTools(this);
MetadataLoader Loader =new MetadataLoader(this);
CodeRegion region=new CodeRegion(this,1);
MetadataTools ef=new MetadataTools(this);
string inputFile=modelNamespace+@".Model\DbContext\\FoodDB.edmx";
EdmItemCollection Itemcollection = Loader.CreateEdmItemCollection(inputFile);
string namespaceName=code.VsNamespaceSuggestion();
EntityFrameworkTemplateFileManager fileManager = EntityFrameworkTemplateFileManager.Create(this);
#>

using <#=modelNamespace#>.Domain.IRepository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Reflection;

namespace <#=modelNamespace#>.RepositoryFactory
{

 public partial class AbstractRepositoryFactory {

 <#
foreach(EntityType entity in Itemcollection.GetItems<EntityType>().OrderBy(e=>e.Name))
{
#>
  public static I<#=entity.Name#>Repository Create_<#=entity.Name#>()
        {
		
            return (I<#=entity.Name#>Repository)CreateInstance(fullClassName+".<#=entity.Name#>Repository");
        }
 <#}#>

  static object CreateInstance(string fullClassName)
        {

            var assembly = Assembly.Load(assemblyName);
            return assembly.CreateInstance(fullClassName);
        }
		static string fullClassName="<#=modelNamespace#>.Repository";
		static string assemblyName="<#=modelNamespace#>.Domain";

 }
  


}
