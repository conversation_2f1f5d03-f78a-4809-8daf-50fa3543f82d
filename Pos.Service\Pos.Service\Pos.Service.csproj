﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{03ED58D1-002A-47FF-9415-349D7FF3737E}</ProjectGuid>
    <OutputType>Exe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Pos.Service</RootNamespace>
    <AssemblyName>Pos.Service</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ComponentApplicationServiceInterface, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null" />
    <Reference Include="ComponentCore, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Pos.Model\Package\ComponentCore.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Class1.cs" />
    <Compile Include="FoodLabelService.cs" />
    <Compile Include="FoodService.cs" />
    <Compile Include="InterfaceService\IFoodLabelService.cs" />
    <Compile Include="InterfaceService\IFoodService.cs" />
    <Compile Include="InterfaceService\IOrderService.cs" />
    <Compile Include="InterfaceService\IRoomService.cs" />
    <Compile Include="IPosService.cs" />
    <Compile Include="OrderService.cs" />
    <Compile Include="PosService.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="RoomService.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Pos.Domain\Pos.Domain.csproj">
      <Project>{48b91ac4-ed2f-48c4-84ff-263cfe5ab65a}</Project>
      <Name>Pos.Domain</Name>
    </ProjectReference>
    <ProjectReference Include="..\Pos.Drive\Pos.Drive.csproj">
      <Project>{e3e2ef69-7dc1-461b-89ef-113e78dc84bd}</Project>
      <Name>Pos.Drive</Name>
    </ProjectReference>
    <ProjectReference Include="..\Pos.Model\Pos.Model.csproj">
      <Project>{0752c409-20ca-41b4-b9b3-bebc6d1602bd}</Project>
      <Name>Pos.Model</Name>
    </ProjectReference>
    <ProjectReference Include="..\Pos.Repository\Pos.Repository.csproj">
      <Project>{a62d073e-370a-48e0-867c-3416be196b23}</Project>
      <Name>Pos.Repository</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>