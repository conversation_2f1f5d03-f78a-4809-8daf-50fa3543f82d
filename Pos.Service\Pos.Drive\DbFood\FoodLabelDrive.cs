﻿using ComponentApplicationServiceInterface.Context.Response;
using Pos.Common;
using Pos.Model.DbContext;
using Pos.Model.DbFood.Conntext;
using Pos.Model.DbFood.RespModel;
using Pos.Model.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Pos.Drive.DbFood
{
    public class FoodLabelDrive : DriveBase
    {
        /// <summary>
        /// 获取门店食品标签列表
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<RespPaginationModel<GetFoodLabelListModel>> GetFoodLabelList(FoodLabelContext context)
        {
            return ActionFun.Run(context, () =>
            {
                var list = app.FoodLabel.GetFoodLabelList(context);

                var resultData = RespPaginationModel<GetFoodLabelListModel>.Package(context.pagination, list);

                return resultData;
            });
        }

        /// <summary>
        /// 批量导入（存在的就进行修改，不存在的就进行插入）
        /// </summary>
        /// <returns></returns>
        public ResponseContext<int> BatchImport(List<BatchImportContext> context)
        {
            return ActionFun.Run(context, () =>
            {
                //不存在食品标签的数据
                int count = 0;
                var noFoods = new List<string>();
                //待插入数据
                var insertData = new List<FoodLabel>();
                var fdNos = context.Select(w => w.FdNo).Distinct().ToList();
                //数据库已存在标签数据
                var dbFoodLabelList = app.FoodLabel.IQueryable(w => fdNos.Contains(w.FdNo)).ToList();
                var dbFoodList = app.Food.IQueryable(w => fdNos.Contains(w.FdNo)).Select(w => new
                {
                    FdNo = w.FdNo,
                    FdCName = w.FdCName
                }).ToList();
                int foodCodeIndex = app.FoodLabel.GetMaxCode();
                context.ForEach(w =>
                {
                    if (w.TypeId.HasValue)
                        w.Type = ((FdLabelTypeEnum)w.TypeId.Value).GetEnumDescription();
                    if (w.CateId.HasValue && string.IsNullOrEmpty(w.Category1))
                        w.Category1 = ((FdLabelCategoryEnum)w.CateId.Value).GetEnumDescription();

                    var food = dbFoodList.FirstOrDefault(x => x.FdNo == w.FdNo);
                    if (food == null)
                    {
                        noFoods.Add(w.FdNo);
                        return;
                    }

                    var label = dbFoodLabelList.FirstOrDefault(x => x.FdNo == w.FdNo);
                    //如果存在，就修改数据否则就插入数据
                    if (label != null)
                    {
                        label.RoomType = w.RoomType;
                        label.Type = w.Type;
                        label.PeoNumber = w.PeoNumber;
                        label.Platform = w.Platform;
                        label.Depart = w.Depart;
                        label.BeverageType = w.BeverageType;
                        label.SnackType = w.SnackType;
                        label.Unit = w.Unit;
                        label.MemberMode = w.MemberMode;
                        label.CardType = w.CardType;
                        label.CashType = w.CashType;
                        label.HeadType = w.HeadType;
                        label.Period = w.Period;
                        label.Bank = w.Bank;
                        label.VoucherType = w.VoucherType;
                        label.MemberCard = w.MemberCard;
                        label.MemberLevel = w.MemberLevel;
                        label.Losses = w.Losses;
                        label.PriceType = w.PriceType;
                        label.BJF = w.BJF;
                        label.ServiceCharge = w.ServiceCharge;
                        label.Deduction = w.Deduction;
                        label.Hours = w.Hours;
                        label.Quantity = w.Quantity;
                        label.Integral = w.Integral;
                        label.PackageCost = w.PackageCost;
                        label.PackageType = w.PackageType;
                        label.GoodsType = w.GoodsType;
                        label.DivideBar = w.DivideBar;
                        label.DivideBuffet = w.DivideBuffet;
                        label.DivideInfield = w.DivideInfield;
                        label.DivideRestaurant = w.DivideRestaurant;
                        label.Category1 = w.Category1;
                        label.Category2 = w.Category2;

                        count += app.FoodLabel.Update(label);
                    }
                    else
                    {
                        var labelData = EntityConversion.Map<FoodLabel>(w);
                        labelData.FoodCode = (++foodCodeIndex).ToString();
                        insertData.Add(labelData);
                    }
                });

                if (insertData.Count > 0)
                {
                    app.FoodLabel.Insert(insertData);
                    count = app.FoodLabel.SaveChanges();
                }

                if (noFoods.Count > 0)
                    throw new ExMessage("以下食品编号不存在，请检查！" + string.Join(",", noFoods));

                return count;
            });
        }

        /// <summary>
        /// 创建食品标签列表
        /// </summary>
        /// <returns></returns>
        public ResponseContext<int> CreateLabel(CreateLabelContext context)
        {
            return ActionFun.Run(context, () =>
            {
                var result = 0;
                try
                {
                    if (string.IsNullOrEmpty(context.FdNo))
                        throw new ExMessage("请输入食品编号！");

                    var food = app.Food.GetFood(context.FdNo);
                    if (food == null)
                        throw new ExMessage("食品信息不存在！");

                    int index = app.FoodLabel.GetMaxCode();
                    var foodLabel = EntityConversion.Map<FoodLabel>(context);
                    foodLabel.FoodCode = (++index).ToString();
                    if (context.TypeId.HasValue)
                        foodLabel.Type = ((FdLabelTypeEnum)context.TypeId.Value).GetEnumDescription();
                    if (context.CateId.HasValue)
                        foodLabel.Category1 = ((FdLabelCategoryEnum)context.CateId.Value).GetEnumDescription();

                    result = app.FoodLabel.Insert(foodLabel);
                }
                catch (Exception ex)
                {
                    ex.HandlerException();
                }
                return result;
            });
        }

        /// <summary>
        /// 获取食品标签详细信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<GetFoodLabelInfoModel> GetLabelInfo(GetFoodLabelInfoContext context)
        {
            return ActionFun.Run(context, () =>
            {
                var data = (from label in app.FoodLabel.IQueryable().AsEnumerable()
                            join food in app.Food.IQueryable().AsEnumerable() on label.FdNo equals food.FdNo
                            join type in app.FdType.IQueryable().AsEnumerable() on food.FtNo equals type.FtNo
                            where label.FoodCode == context.FoodCode
                            select new GetFoodLabelInfoModel()
                            {
                                FdCName = food.FdCName,
                                FtCName = type.FtCName,
                                FoodLabel = label
                            }).FirstOrDefault();

                if (data == null)
                    throw new Exception("所选择的视频标签不存在！");

                if (!string.IsNullOrEmpty(data.FoodLabel.Type))
                    data.TypeId = EnumHelper.GetValue<FdLabelTypeEnum>(data.FoodLabel.Type);
                if (!string.IsNullOrEmpty(data.FoodLabel.Category1))
                    data.CateId = EnumHelper.GetValue<FdLabelCategoryEnum>(data.FoodLabel.Category1);

                return data;
            });
        }

        /// <summary>
        /// 删除标签
        /// </summary>
        /// <returns></returns>
        public ResponseContext<int> Delete(DeleteFoodLabelContext context)
        {
            return ActionFun.Run(context, () =>
            {
                var label = app.FoodLabel.FindEntity(x => x.FoodCode == context.FoodCode);
                if (label == null)
                    throw new Exception("要删除的食品标签不存在！");

                return app.FoodLabel.Delete(label);
            });
        }

        public ResponseContext<List<KeyValuePair<string, string>>> GetFtDropDown(GetFtDropDownContext context)
        {
            return ActionFun.Run(context, () =>
            {
                var data = app.FdType.IQueryable().ToList().Select(x => new KeyValuePair<string, string>(x.FtCName, x.FtNo)).ToList();

                return data;
            });
        }

        public ResponseContext<List<KeyValuePair<int, string>>> GetTypes(GetFtDropDownContext context)
        {
            return ActionFun.Run(context, () =>
            {
                return typeof(FdLabelTypeEnum).GetEnumList();
            });
        }

        public ResponseContext<List<KeyValuePair<int, string>>> GetCates(GetFtDropDownContext context)
        {
            return ActionFun.Run(context, () =>
            {
                return typeof(FdLabelCategoryEnum).GetEnumList();
            });
        }
    }
}
