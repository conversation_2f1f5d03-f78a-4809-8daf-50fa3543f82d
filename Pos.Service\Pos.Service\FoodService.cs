﻿using ComponentApplicationServiceInterface.Context.Response;
using Pos.Drive.DbFood;
using Pos.Model.DbFood.Context;
using Pos.Model.DbFood.RespModel;
using Pos.Service.InterfaceService;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Pos.Service
{
    public partial class PosService : IFoodService
    {
        public ResponseContext<List<GetHeadCountModel>> GetHeadCountReport(GetStoreReportContext context)
        {
            return new FoodDrive().GetHeadCountReport(context);
        }

        public ResponseContext<List<GetStoreReportModel>> GetStoreReport(GetStoreReportContext context)
        {
            return new FoodDrive().GetStoreReport(context);
        }

        public ResponseContext<List<GetStoreFtTypeReportModel>> GetTypeDetailReport(GetStoreFtTypeReportContext context)
        {
            return new FoodDrive().GetTypeDetailReport(context);
        }

        public ResponseContext<List<WaitCreateModel>> GetWaitCreate(WaitCreateContext context)
        {
            return new FoodDrive().GetWaitCreate(context);
        }

        public ResponseContext<List<GetFoodInfoModel>> GetFoodInfo(GetFoodInfoContext context)
        {
            return new FoodDrive().GetFoodInfo(context);
        }
    }
}
