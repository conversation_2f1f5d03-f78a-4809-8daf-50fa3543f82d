﻿using ComponentApplicationServiceInterface.Context.Response;
using Pos.Model.DbFood.Conntext;
using Pos.Model.DbFood.RespModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Pos.Service.InterfaceService
{
    [ServiceContract]
    public interface IFoodLabelService
    {
        [OperationContract]
        ResponseContext<RespPaginationModel<GetFoodLabelListModel>> GetFoodLabelList(FoodLabelContext context);

        [OperationContract]
        ResponseContext<int> BatchImport(List<BatchImportContext> context);

        [OperationContract]
        ResponseContext<GetFoodLabelInfoModel> GetLabelInfo(GetFoodLabelInfoContext context);

        [OperationContract]
        ResponseContext<int> Delete(DeleteFoodLabelContext context);

        [OperationContract]
        ResponseContext<List<KeyValuePair<string, string>>> GetFtDropDown(GetFtDropDownContext context);

        [OperationContract]
        ResponseContext<int> CreateLabel(CreateLabelContext context);

        [OperationContract]
        ResponseContext<List<KeyValuePair<int, string>>> GetTypes(GetFtDropDownContext context);

        [OperationContract]
        ResponseContext<List<KeyValuePair<int, string>>> GetCates(GetFtDropDownContext context);
    }
}
