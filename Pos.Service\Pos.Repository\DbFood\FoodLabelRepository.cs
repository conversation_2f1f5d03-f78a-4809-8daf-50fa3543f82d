﻿using Pos.Domain.IRepository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Pos.Repository
{
    public partial class FoodLabelRepository : BaseRepository<Pos.Model.DbContext.FoodLabel>, IFoodLabelRepository
    {
        public int GetMaxCode()
        {
            string sql = "SELECT FoodCode FROM [dbo].[FoodLabel] ORDER BY CAST(FoodCode AS INT) DESC;";
            var foodCode = db.Database.SqlQuery<string>(sql).FirstOrDefault();
            return int.Parse(foodCode);
        }
    }
}
