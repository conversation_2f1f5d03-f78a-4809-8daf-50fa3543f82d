﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace Pos.Common
{
    public class LogHelper
    {
        static object lockErrorObj = new object();
        private static readonly string _logDir = AppDomain.CurrentDomain.BaseDirectory + "\\Log";



        /// <summary>
        /// 创建文件夹
        /// </summary>
        private static void CreateLogDir()
        {
            if (!Directory.Exists(_logDir))
                Directory.CreateDirectory(_logDir);
        }

        /// <summary>
        /// 创建文件夹里面的日志文件（一天创建一个）
        /// </summary>
        /// <param name="strlogFile">文件名称和地址</param>
        private static void CreateLogFile(string strlogFile)
        {
            if (!File.Exists(strlogFile))
                File.Create(strlogFile).Dispose();
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="message"></param>
        public static void Error(string message)
        {
            string filePath = _logDir + "\\log_error" + DateTime.Now.ToString("yyyy-MM-dd") + ".txt";
            CreateLogDir();
            CreateLogFile(filePath);

            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.AppendLine("请求时间：" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            stringBuilder.AppendLine("异常信息：" + message);
            stringBuilder.AppendLine();
            lock (lockErrorObj)
            {
                File.AppendAllLines(filePath, new string[]
                {
                    stringBuilder.ToString()
                });
            }
        }
    }
}
