﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;

namespace Pos.Common
{
    public class EntityConversion
    {
        public static T Map<T>(object source)
        {
            var targetType = typeof(T);
            var sourceType = source.GetType();
            T destination = Activator.CreateInstance<T>();

            var targetProperties = targetType.GetProperties();
            foreach (var targetProperty in targetProperties)
            {
                var sourceProperty = sourceType.GetProperty(targetProperty.Name, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                if (sourceProperty != null && sourceProperty.CanRead && targetProperty.CanWrite)
                {
                    var sourceValue = sourceProperty.GetValue(source, null);
                    if (sourceValue != null && targetProperty.PropertyType.IsAssignableFrom(sourceProperty.PropertyType))
                    {
                        targetProperty.SetValue(destination, sourceValue, null);
                    }
                    //判断是否是一个class类型，并且不是一个基元类型，并且不是string类型
                    else if (targetProperty.PropertyType.IsClass && !targetProperty.PropertyType.IsPrimitive && targetProperty.PropertyType != typeof(string))
                    {
                        var nestedDestination = targetProperty.GetValue(destination, null);
                        if (nestedDestination == null)
                        {
                            nestedDestination = Activator.CreateInstance(targetProperty.PropertyType);
                            targetProperty.SetValue(destination, nestedDestination, null);
                        }

                        Map(nestedDestination, sourceValue);
                    }
                }
            }

            return destination;
        }

        private static void Map(object destination, object source)
        {
            var destinationType = destination.GetType();
            var sourceType = source.GetType();

            var destinationProperties = destinationType.GetProperties();
            foreach (var destinationProperty in destinationProperties)
            {
                var sourceProperty = sourceType.GetProperty(destinationProperty.Name, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                if (sourceProperty != null && sourceProperty.CanRead && destinationProperty.CanWrite)
                {
                    var sourceValue = sourceProperty.GetValue(source, null);
                    if (sourceValue != null)
                    {
                        if (destinationProperty.PropertyType.IsAssignableFrom(sourceProperty.PropertyType))
                        {
                            destinationProperty.SetValue(destination, sourceValue, null);
                        }
                        else if (destinationProperty.PropertyType.IsClass && !destinationProperty.PropertyType.IsPrimitive)
                        {
                            var nestedDestination = destinationProperty.GetValue(destination, null);
                            if (nestedDestination == null)
                            {
                                nestedDestination = Activator.CreateInstance(destinationProperty.PropertyType);
                                destinationProperty.SetValue(destination, nestedDestination, null);
                            }

                            Map(nestedDestination, sourceValue);
                        }
                    }
                }
            }
        }
    }
}
