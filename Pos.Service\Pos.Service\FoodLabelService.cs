﻿using ComponentApplicationServiceInterface.Context.Response;
using Pos.Drive.DbFood;
using Pos.Model.DbFood.Conntext;
using Pos.Model.DbFood.RespModel;
using Pos.Service.InterfaceService;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Pos.Service
{
    public partial class PosService : IFoodLabelService
    {
        public ResponseContext<int> BatchImport(List<BatchImportContext> context)
        {
            return new FoodLabelDrive().BatchImport(context);
        }

        public ResponseContext<int> Delete(DeleteFoodLabelContext context)
        {
            return new FoodLabelDrive().Delete(context);
        }

        public ResponseContext<RespPaginationModel<GetFoodLabelListModel>> GetFoodLabelList(FoodLabelContext context)
        {
            return new FoodLabelDrive().GetFoodLabelList(context);
        }

        public ResponseContext<GetFoodLabelInfoModel> GetLabelInfo(GetFoodLabelInfoContext context)
        {
            return new FoodLabelDrive().GetLabelInfo(context);
        }

        public ResponseContext<List<KeyValuePair<string, string>>> GetFtDropDown(GetFtDropDownContext context)
        {
            return new FoodLabelDrive().GetFtDropDown(context);
        }

        public ResponseContext<int> CreateLabel(CreateLabelContext context)
        {
            return new FoodLabelDrive().CreateLabel(context);
        }

        public ResponseContext<List<KeyValuePair<int, string>>> GetTypes(GetFtDropDownContext context)
        {
            return new FoodLabelDrive().GetTypes(context);
        }

        public ResponseContext<List<KeyValuePair<int, string>>> GetCates(GetFtDropDownContext context)
        {
            return new FoodLabelDrive().GetCates(context);
        }
    }
}
