﻿using ComponentApplicationServiceInterface.Context.Response;
using Pos.Drive.DbFood;
using Pos.Model.DbFood.Conntext;
using Pos.Model.DbFood.RespModel;
using Pos.Service.InterfaceService;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Pos.Service
{
    public partial class PosService : IRoomService
    {
        public ResponseContext<GetRoomInfoModel> GetRoomInfo(GetRoomInfoContext context)
        {
            return new RoomServiceDrive().GetRoomInfo(context);
        }
    }
}
