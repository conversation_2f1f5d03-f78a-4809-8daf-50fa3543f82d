﻿using ComponentApplicationServiceInterface.Repository;
using Pos.Model.DbContext;
using Pos.Model.DbFood.Context;
using Pos.Model.DbFood.RespModel;
using Pos.RepositoryFactory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Pos.Application
{
    public partial class FoodApp : AppBase<Food>
    {
        public List<StoreReportModel> GetStoreReport(GetStoreReportContext context)
        {
            return Repository.Food.GetStoreReport(context);
        }

        public List<GetStoreFtTypeReportModel> GetTypeDetailReport(GetStoreFtTypeReportContext context)
        {
            return Repository.Food.GetDetailReport(context);
        }

        public List<HeadCountModel> GetHeadCountReport(GetStoreReportContext context)
        {
            return Repository.Food.GetHeadCountReport(context);
        }

        public List<WaitCreateModel> GetWaitCreate()
        {
            return Repository.Food.GetWaitCreate();
        }

        public GetFoodModel GetFood(string fdNo)
        {
            return Repository.Food.GetFood(fdNo);
        }
    }
}
