//------------------------------------------------------------------------------
// <auto-generated>
//    此代码是根据模板生成的。
//
//    手动更改此文件可能会导致应用程序中发生异常行为。
//    如果重新生成代码，则将覆盖对此文件的手动更改。
// </auto-generated>
//------------------------------------------------------------------------------

namespace Pos.Model.DbContext
{
    using System;
    using System.Collections.Generic;
    
    public partial class Roomtest
    {
        public string RmNo { get; set; }
        public string RmName { get; set; }
        public string RtNo { get; set; }
        public string AreaNo { get; set; }
        public string InvNo { get; set; }
        public string PriceNo { get; set; }
        public string WorkDate { get; set; }
        public string RsPos { get; set; }
        public string RmStatus { get; set; }
        public bool IsSDate { get; set; }
        public string Rem { get; set; }
        public string BookDate { get; set; }
        public string BookTime { get; set; }
        public string InDate { get; set; }
        public string InTime { get; set; }
        public Nullable<short> InNumbers { get; set; }
        public string OpenUserId { get; set; }
        public string AccUserId { get; set; }
        public string AccDate { get; set; }
        public string AccTime { get; set; }
        public string ContinueUserId { get; set; }
        public string ContinueTime { get; set; }
        public string MemberNo { get; set; }
        public string CustName { get; set; }
        public string OrderUserId { get; set; }
        public int DiscRate { get; set; }
        public Nullable<int> Serv { get; set; }
        public Nullable<int> FdCost { get; set; }
        public Nullable<int> RmCost { get; set; }
        public Nullable<int> Disc { get; set; }
        public Nullable<int> ZD { get; set; }
        public Nullable<int> BeerZD { get; set; }
        public Nullable<int> BeerCash { get; set; }
        public Nullable<int> Tax { get; set; }
        public int MorePayed { get; set; }
        public Nullable<int> Tot { get; set; }
        public bool WC { get; set; }
        public bool Dance { get; set; }
        public string PrnFIndex { get; set; }
        public string PrnDIndex { get; set; }
        public Nullable<short> PInvCount { get; set; }
        public string FromRmNo { get; set; }
        public Nullable<short> OpenCount { get; set; }
        public bool ForceNoServ { get; set; }
        public Nullable<int> Tag { get; set; }
        public int FixedDisc { get; set; }
        public string CarId { get; set; }
        public Nullable<int> FdCost_InRmCost { get; set; }
        public Nullable<int> FdCost_NotInRmCost { get; set; }
        public bool MembDisc { get; set; }
        public Nullable<int> MembCard { get; set; }
        public string Card_MNo { get; set; }
        public Nullable<int> CardAmount { get; set; }
        public string CloseTime { get; set; }
        public bool CallAccount { get; set; }
        public string BadReason { get; set; }
        public string BadUserId { get; set; }
        public int AutoZD { get; set; }
        public System.Guid rowguid { get; set; }
        public System.Guid msrepl_tran_version { get; set; }
        public int th_RmCost { get; set; }
    }
}
