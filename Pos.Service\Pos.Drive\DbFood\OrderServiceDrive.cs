﻿using ComponentApplicationServiceInterface.Context.Response;
using Pos.Common.Global;
using Pos.Model.DbFood.Conntext;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Pos.Drive.DbFood
{
    public class OrderServiceDrive : DriveBase
    {
        public ResponseContext<bool> PlaceOrder(PlaceOrderContext model)
        {
            return ActionFun.Run(model, () =>
            {
                return app.FdInv.PlaceOrder(model);
            });
        }

        /// <summary>
        /// 获取天王下单人头数
        /// </summary>
        /// <returns></returns>
        public ResponseContext<int> GetOrderNumbers(GetOrderNumbersContext context)
        {
            return ActionFun.Run(context, () =>
            {
                var fdcash = app.FdCash.IQueryable(w => w.FdNo == GlobalConfig.HeadFdNo && w.RmNo == context.RmNo).Select(w => new
                {
                    w.FdNo,
                    w.FdQty
                });

                return fdcash.Sum(w => w.FdQty);
            });
        }
    }
}
