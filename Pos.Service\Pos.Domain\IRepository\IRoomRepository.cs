﻿using ComponentApplicationServiceInterface.Repository;
using Pos.Model.DbContext;
using Pos.Model.DbFood.Conntext;
using Pos.Model.DbFood.RespModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Pos.Domain.IRepository
{
    public partial interface IRoomRepository : IRepositoryBase<Room>
    {
        GetRoomInfoModel GetRoomInfo(GetRoomInfoContext context);
    }
}
