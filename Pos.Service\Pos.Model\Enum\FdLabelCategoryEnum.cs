﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;

namespace Pos.Model.Enum
{
    public enum FdLabelCategoryEnum
    {
        /// <summary>
        /// 纯自助餐-正价
        /// </summary>
        [Description("纯自助餐")]
        Buffet = 1,

        /// <summary>
        /// 纯自助餐-正价
        /// </summary>
        [Description("K+自助餐")]
        SingAndBuffet = 2,

        /// <summary>
        /// 纯自助餐-正价
        /// </summary>
        [Description("K+自助餐团购")]
        SingAndBuffetGroup = 3,

        /// <summary>
        /// 非会员价K+自助餐
        /// </summary>
        [Description("非会员价K+自助餐")]
        NonMemberKAndBuffet_ZL = 4,

        /// <summary>
        /// K+自助餐银行活动
        /// </summary>
        [Description("K+自助餐银行活动")]
        SingAndBuffetBank = 5,

        /// <summary>
        /// 赠送类
        /// </summary>
        [Description("赠送类")]
        Give = 6,
    }
}
