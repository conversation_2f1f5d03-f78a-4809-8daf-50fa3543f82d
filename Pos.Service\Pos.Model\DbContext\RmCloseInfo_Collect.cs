//------------------------------------------------------------------------------
// <auto-generated>
//    此代码是根据模板生成的。
//
//    手动更改此文件可能会导致应用程序中发生异常行为。
//    如果重新生成代码，则将覆盖对此文件的手动更改。
// </auto-generated>
//------------------------------------------------------------------------------

namespace Pos.Model.DbContext
{
    using System;
    using System.Collections.Generic;
    
    public partial class RmCloseInfo_Collect
    {
        public string InvNo { get; set; }
        public int Cash { get; set; }
        public int Cash_Targ { get; set; }
        public int Vesa { get; set; }
        public string VesaName { get; set; }
        public string VesaNo { get; set; }
        public int Vesa_Targ { get; set; }
        public string VesaName_Targ { get; set; }
        public string VesaNo_Targ { get; set; }
        public int GZ { get; set; }
        public string GZName { get; set; }
        public int AccOkZD { get; set; }
        public string ZDName { get; set; }
        public int NoPayed { get; set; }
        public string NoPayedName { get; set; }
        public int Check { get; set; }
        public string CheckName { get; set; }
        public int WXPay { get; set; }
        public string OpenId { get; set; }
        public string wx_out_trade_no { get; set; }
        public int AliPay { get; set; }
        public string user_id { get; set; }
        public string Ali_out_trade_no { get; set; }
        public int MTPay { get; set; }
        public string MTPayNo { get; set; }
        public int DZPay { get; set; }
        public string DZPayNo { get; set; }
        public int NMPay { get; set; }
        public string NMPayNo { get; set; }
        public int Coupon { get; set; }
        public string CouponName { get; set; }
        public int RechargeAccount { get; set; }
        public string RechargeMemberCardNo { get; set; }
        public int ReturnAccount { get; set; }
        public string ReturnMemberCardNo { get; set; }
        public System.DateTime CloseDatetime { get; set; }
        public System.Guid MemberKey { get; set; }
        public string CloseUserName { get; set; }
        public string CloseUserId { get; set; }
        public int WechatDeposit { get; set; }
        public int WechatShopping { get; set; }
        public int Tot { get; set; }
        public string FtTime { get; set; }
        public int InNumbers { get; set; }
        public int IsNoOne { get; set; }
    }
}
