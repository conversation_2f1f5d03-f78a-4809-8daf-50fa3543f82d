﻿using Pos.Common;
using Pos.Domain.IRepository;
using Pos.Model.DbContext;
using Pos.Model.DbFood.Conntext;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace Pos.Repository
{
    public partial class FdInvRepository : BaseRepository<FdInv>, IFdInvRepository
    {
        public bool PlaceOrder(PlaceOrderContext data)
        {
            StringBuilder sb = new StringBuilder("declare @ZDAmountLeft int   Create Table #OrderTable (FdNo nvarchar(5) collate Chinese_PRC_Stroke_CI_AS,FdQty int,FdPrice int,Ai nvarchar(100) collate Chinese_PRC_Stroke_CI_AS,AiCost int)");
            foreach (var item in data.Items)
            {
                sb.Append(" Insert into #OrderTable (FdNo,FdQty,FdPrice,Ai,AiCost)values('" + item.FdNo + "'," + item.FdQty + "," + item.FdPrice + ",'" + item.Ai + "',0)");
            }
            //sb.Append("exec DoOrder '" + data.RmNo + "','" + data.CashType + "','" + data.CashUserId + "','" + data.InputUserId + "',@ZDAmountLeft output");
            sb.Append("exec DoOrder @RmNo,@CashType,@CashUserId,@InputUserId,@ZDAmountLeft output");
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("@RmNo",data.RmNo),
                new SqlParameter("@CashType",data.CashType),
                new SqlParameter("@CashUserId",data.CashUserId),
                new SqlParameter("@InputUserId",data.InputUserId),
            }.ToArray();

            LogHelper.Error("SQL:" + sb.ToString());

            return db.Database.ExecuteSqlCommand(sb.ToString(), parameters) > 0;
        }
    }
}
