﻿using ComponentApplicationServiceInterface.Context.Response;
using Pos.Model.DbFood.Conntext;
using Pos.Model.DbFood.RespModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Pos.Service.InterfaceService
{
    [ServiceContract]
    public interface IRoomService
    {
        [OperationContract]
        ResponseContext<GetRoomInfoModel> GetRoomInfo(GetRoomInfoContext context);
    }
}
