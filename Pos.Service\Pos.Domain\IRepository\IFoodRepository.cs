﻿using ComponentApplicationServiceInterface.Repository;
using Pos.Model.DbFood.Context;
using Pos.Model.DbFood.RespModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Pos.Domain.IRepository
{
    public partial interface IFoodRepository : IRepositoryBase<Pos.Model.DbContext.Food>
    {
        List<StoreReportModel> GetStoreReport(GetStoreReportContext context);

        List<GetStoreFtTypeReportModel> GetDetailReport(GetStoreFtTypeReportContext context);

        List<HeadCountModel> GetHeadCountReport(GetStoreReportContext context);

        List<WaitCreateModel> GetWaitCreate();

        GetFoodModel GetFood(string fdNo);
    }
}
