﻿using Pos.Model.DbContext;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Pos.Model.DbFood.RespModel
{
    public class GetStoreReportModel
    {
        /// <summary>
        /// 下单类别（系统编号）
        /// </summary>
        public string FtNo { get; set; }

        /// <summary>
        /// 类别名称（项目）
        /// </summary>
        public string FtCName { get; set; }

        /// <summary>
        /// 收入总金额
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// 酒吧分成汇总
        /// </summary>
        public decimal BarDivision { get; set; }

        /// <summary>
        /// 自助餐分成汇总
        /// </summary>
        public decimal BuffetDivision { get; set; }

        /// <summary>
        /// 夜班小吃分成
        /// </summary>
        public decimal NightDivision { get; set; }

        /// <summary>
        /// 内场分成
        /// </summary>
        public decimal InfieldDivision { get; set; }
    }

    public class StoreReportModel
    {
        public string InvNo { get; set; }

        public string FdNo { get; set; }

        public string FdCName { get; set; }

        public int FdPrice { get; set; }

        public short FdQty { get; set; }

        public string CashTime { get; set; }

        public string CashType { get; set; }

        public string CashUserName { get; set; }

        public string WorkDate { get; set; }

        public string FtNo { get; set; }

        public string FtCName { get; set; }

        public string PrnType { get; set; }

        public int IsPackage { get; set; }
    }

    public class GetStoreFtTypeReportModel
    {
        public string InvNo { get; set; }
        public string FdNo { get; set; }
        public string FdCName { get; set; }
        public int FdPrice { get; set; }
        public int FdQty { get; set; }
        public string CashTime { get; set; }
        public string FdCashType { get; set; }
        public string CashUserName { get; set; }
        public Nullable<int> RoomType { get; set; }
        public string Type { get; set; }
        public Nullable<int> PeoNumber { get; set; }
        public Nullable<int> Platform { get; set; }
        public string Depart { get; set; }
        public Nullable<int> BeverageType { get; set; }
        public Nullable<int> SnackType { get; set; }
        public Nullable<int> Unit { get; set; }
        public Nullable<bool> MemberMode { get; set; }
        public Nullable<int> CardType { get; set; }
        public Nullable<int> CashType { get; set; }
        public Nullable<int> HeadType { get; set; }
        public string Period { get; set; }
        public Nullable<int> Bank { get; set; }
        public Nullable<int> VoucherType { get; set; }
        public Nullable<int> MemberCard { get; set; }
        public Nullable<int> MemberLevel { get; set; }
        public Nullable<int> Losses { get; set; }
        public Nullable<int> PriceType { get; set; }
        public Nullable<int> BJF { get; set; }
        public Nullable<int> ServiceCharge { get; set; }
        public Nullable<int> Deduction { get; set; }
        public Nullable<int> Hours { get; set; }
        public Nullable<int> Quantity { get; set; }
        public Nullable<int> Integral { get; set; }
        public Nullable<int> PackageCost { get; set; }
        public Nullable<int> PackageType { get; set; }
        public Nullable<int> GoodsType { get; set; }
        public Nullable<decimal> DivideBar { get; set; }
        public Nullable<decimal> DivideRestaurant { get; set; }
        public Nullable<decimal> DivideInfield { get; set; }
        public Nullable<decimal> DivideBuffet { get; set; }
    }

    public class HeadCountModel
    {
        public string Category1 { get; set; }
        public string Category2 { get; set; }
        public int PeoNumber { get; set; }
        public string Type { get; set; }
    }

    public class GetHeadCountModel
    {


        /// <summary>
        /// 经营类别
        /// </summary>
        public string Business { get; set; }

        /// <summary>
        /// 类别明细
        /// </summary>
        public List<HeadCountDetail> Details { get; set; }
    }

    public class HeadCountDetail
    {
        /// <summary>
        /// 序号
        /// </summary>
        public int Index { get; set; }

        /// <summary>
        /// 类别1
        /// </summary>
        public string Category1 { get; set; }

        /// <summary>
        /// 类别2
        /// </summary>
        public string Category2 { get; set; }

        /// <summary>
        /// 人头数量
        /// </summary>
        public int PeoCount { get; set; }
    }

    public class UnifiedComputeModel
    {
        /// <summary>
        /// 编号
        /// </summary>
        public string No { get; set; }
        /// <summary>
        /// 销售金额（单价*数量）
        /// </summary>
        public decimal TotalPrice { get { return FdQty * FdPrice; } }
        /// <summary>
        /// 商品分类
        /// </summary>
        public string FtNo { get; set; }
        /// <summary>
        /// 分类名称
        /// </summary>
        public string FtName { get; set; }

        /// <summary>
        /// 酒吧分成比例
        /// </summary>
        public decimal BarDivision { get; set; }

        /// <summary>
        /// 自助餐分成比例
        /// </summary>
        public decimal BuffetDivision { get; set; }

        /// <summary>
        /// 夜班小吃分成比例
        /// </summary>
        public decimal NightDivision { get; set; }

        /// <summary>
        /// 内场分成比例
        /// </summary>
        public decimal InfieldDivision { get; set; }

        public int FdQty { get; set; }

        public int FdPrice { get; set; }

        /// <summary>
        /// 套餐数据（不是套餐就为null）
        /// </summary>
        public PackageProductModel Package { get; set; }
    }

    /// <summary>
    /// 套餐模型
    /// </summary>
    public class PackageProductModel
    {
        /// <summary>
        /// 套餐中包含餐饮模块的
        /// </summary>
        public List<DivisionModel> VightFood { get; set; }

        /// <summary>
        /// 套餐中包含酒吧模块的
        /// </summary>
        public List<DivisionModel> BarFood { get; set; }
    }

    /// <summary>
    /// 分成模块
    /// </summary>
    public class DivisionModel
    {
        public string FdNo { get; set; }

        public decimal TotalPrice { get { return FdQty * FdPrice; } }

        public int FdPrice { get; set; }

        public short FdQty { get; set; }
    }

    public class GetFoodInfoModel
    {
        public string FdNo { get; set; }

        public string FdCName { get; set; }
    }

    public class GetFoodModel 
    {
        public string FdNo { get; set; }

        public string FdCName { get; set; }

    }
}
