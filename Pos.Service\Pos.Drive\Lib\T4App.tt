﻿<#@ template debug="false" hostspecific="false" language="C#" #>
<#@ include file="EF.Utility.CS.ttinclude"#>
<#@ output extension=".cs" #>
<#
string   modelNamespace="Pos";
CodeGenerationTools code =new CodeGenerationTools(this);
MetadataLoader Loader =new MetadataLoader(this);
CodeRegion region=new CodeRegion(this,1);
MetadataTools ef=new MetadataTools(this);
string inputFile=modelNamespace+@".Model\DbContext\\FoodDB.edmx";
EdmItemCollection Itemcollection = Loader.CreateEdmItemCollection(inputFile);
string namespaceName=code.VsNamespaceSuggestion();
EntityFrameworkTemplateFileManager fileManager = EntityFrameworkTemplateFileManager.Create(this);
#>
using <#=modelNamespace#>.Application;
using <#=modelNamespace#>.Model.DbContext;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
namespace <#=modelNamespace#>.Drive
{
 public partial class AppSession {
<#
foreach(EntityType entity in Itemcollection.GetItems<EntityType>().OrderBy(e=>e.Name))
{
#>

  <#=entity.Name#>App _<#=entity.Name#>App;
    public <#=entity.Name#>App <#=entity.Name#>
        {
            get
            {
                if (_<#=entity.Name#>App == null) _<#=entity.Name#>App = new <#=entity.Name#>App();
                return _<#=entity.Name#>App;
            }
        }
        
 

  

<#}#>
 }
}
