﻿




using Pos.Domain.IRepository;
using Pos.Model.DbContext;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Pos.Repository
{

 public partial class AddItemRepository : BaseRepository<Pos.Model.DbContext.AddItem>,IAddItemRepository  {}
  


 public partial class AiTypeRepository : BaseRepository<Pos.Model.DbContext.AiType>,IAiTypeRepository  {}
  


 public partial class AmountLogRepository : BaseRepository<Pos.Model.DbContext.AmountLog>,IAmountLogRepository  {}
  


 public partial class CarLeaveLogRepository : BaseRepository<Pos.Model.DbContext.CarLeaveLog>,ICarLeaveLogRepository  {}
  


 public partial class CashierRepository : BaseRepository<Pos.Model.DbContext.Cashier>,ICashierRepository  {}
  


 public partial class ClearDataLogRepository : BaseRepository<Pos.Model.DbContext.ClearDataLog>,IClearDataLogRepository  {}
  


 public partial class DeadLockLogRepository : BaseRepository<Pos.Model.DbContext.DeadLockLog>,IDeadLockLogRepository  {}
  


 public partial class DepositInfoRepository : BaseRepository<Pos.Model.DbContext.DepositInfo>,IDepositInfoRepository  {}
  


 public partial class DeptRepository : BaseRepository<Pos.Model.DbContext.Dept>,IDeptRepository  {}
  


 public partial class DeptBanSetRepository : BaseRepository<Pos.Model.DbContext.DeptBanSet>,IDeptBanSetRepository  {}
  


 public partial class dtpropertiesRepository : BaseRepository<Pos.Model.DbContext.dtproperties>,IdtpropertiesRepository  {}
  


 public partial class FdCashRepository : BaseRepository<Pos.Model.DbContext.FdCash>,IFdCashRepository  {}
  


 public partial class FdCash_TrackRepository : BaseRepository<Pos.Model.DbContext.FdCash_Track>,IFdCash_TrackRepository  {}
  


 public partial class FdCashBakRepository : BaseRepository<Pos.Model.DbContext.FdCashBak>,IFdCashBakRepository  {}
  


 public partial class FdCashBak_BRepository : BaseRepository<Pos.Model.DbContext.FdCashBak_B>,IFdCashBak_BRepository  {}
  


 public partial class FdCashBak_BakRepository : BaseRepository<Pos.Model.DbContext.FdCashBak_Bak>,IFdCashBak_BakRepository  {}
  


 public partial class FdDetTypeRepository : BaseRepository<Pos.Model.DbContext.FdDetType>,IFdDetTypeRepository  {}
  


 public partial class FdImageRepository : BaseRepository<Pos.Model.DbContext.FdImage>,IFdImageRepository  {}
  


 public partial class FdInvRepository : BaseRepository<Pos.Model.DbContext.FdInv>,IFdInvRepository  {}
  


 public partial class FdInv_BRepository : BaseRepository<Pos.Model.DbContext.FdInv_B>,IFdInv_BRepository  {}
  


 public partial class FdInv_BakRepository : BaseRepository<Pos.Model.DbContext.FdInv_Bak>,IFdInv_BakRepository  {}
  


 public partial class FdInv_ExchangeLogRepository : BaseRepository<Pos.Model.DbContext.FdInv_ExchangeLog>,IFdInv_ExchangeLogRepository  {}
  


 public partial class FdInvCashItemRepository : BaseRepository<Pos.Model.DbContext.FdInvCashItem>,IFdInvCashItemRepository  {}
  


 public partial class FdInvDescRepository : BaseRepository<Pos.Model.DbContext.FdInvDesc>,IFdInvDescRepository  {}
  


 public partial class FdTicketRepository : BaseRepository<Pos.Model.DbContext.FdTicket>,IFdTicketRepository  {}
  


 public partial class FdTimePriceRepository : BaseRepository<Pos.Model.DbContext.FdTimePrice>,IFdTimePriceRepository  {}
  


 public partial class FdTimeZoneRepository : BaseRepository<Pos.Model.DbContext.FdTimeZone>,IFdTimeZoneRepository  {}
  


 public partial class FdTypeRepository : BaseRepository<Pos.Model.DbContext.FdType>,IFdTypeRepository  {}
  


 public partial class FdUserRepository : BaseRepository<Pos.Model.DbContext.FdUser>,IFdUserRepository  {}
  


 public partial class FdUserGradeRepository : BaseRepository<Pos.Model.DbContext.FdUserGrade>,IFdUserGradeRepository  {}
  


 public partial class FdUserRightsRepository : BaseRepository<Pos.Model.DbContext.FdUserRights>,IFdUserRightsRepository  {}
  


 public partial class FestivalTimeRepository : BaseRepository<Pos.Model.DbContext.FestivalTime>,IFestivalTimeRepository  {}
  


 public partial class FoodRepository : BaseRepository<Pos.Model.DbContext.Food>,IFoodRepository  {}
  


 public partial class FoodCalRepository : BaseRepository<Pos.Model.DbContext.FoodCal>,IFoodCalRepository  {}
  


 public partial class FoodLabelRepository : BaseRepository<Pos.Model.DbContext.FoodLabel>,IFoodLabelRepository  {}
  


 public partial class FoodOrderMRepository : BaseRepository<Pos.Model.DbContext.FoodOrderM>,IFoodOrderMRepository  {}
  


 public partial class FPrnRepository : BaseRepository<Pos.Model.DbContext.FPrn>,IFPrnRepository  {}
  


 public partial class FPrnDataRepository : BaseRepository<Pos.Model.DbContext.FPrnData>,IFPrnDataRepository  {}
  


 public partial class FPrnData_BakRepository : BaseRepository<Pos.Model.DbContext.FPrnData_Bak>,IFPrnData_BakRepository  {}
  


 public partial class FreePackageCoupon_RecordRepository : BaseRepository<Pos.Model.DbContext.FreePackageCoupon_Record>,IFreePackageCoupon_RecordRepository  {}
  


 public partial class FtInfoRepository : BaseRepository<Pos.Model.DbContext.FtInfo>,IFtInfoRepository  {}
  


 public partial class GDDB20InfoRepository : BaseRepository<Pos.Model.DbContext.GDDB20Info>,IGDDB20InfoRepository  {}
  


 public partial class GDDBInfoRepository : BaseRepository<Pos.Model.DbContext.GDDBInfo>,IGDDBInfoRepository  {}
  


 public partial class HappyRabRepository : BaseRepository<Pos.Model.DbContext.HappyRab>,IHappyRabRepository  {}
  


 public partial class HolidayRepository : BaseRepository<Pos.Model.DbContext.Holiday>,IHolidayRepository  {}
  


 public partial class HotFdTypeRepository : BaseRepository<Pos.Model.DbContext.HotFdType>,IHotFdTypeRepository  {}
  


 public partial class HotFoodRepository : BaseRepository<Pos.Model.DbContext.HotFood>,IHotFoodRepository  {}
  


 public partial class Inv_TimeSectionRepository : BaseRepository<Pos.Model.DbContext.Inv_TimeSection>,IInv_TimeSectionRepository  {}
  


 public partial class InvRollBackRepository : BaseRepository<Pos.Model.DbContext.InvRollBack>,IInvRollBackRepository  {}
  


 public partial class LanIdRepository : BaseRepository<Pos.Model.DbContext.LanId>,ILanIdRepository  {}
  


 public partial class LanStringRepository : BaseRepository<Pos.Model.DbContext.LanString>,ILanStringRepository  {}
  


 public partial class LastInvNoRepository : BaseRepository<Pos.Model.DbContext.LastInvNo>,ILastInvNoRepository  {}
  


 public partial class LastRefNoRepository : BaseRepository<Pos.Model.DbContext.LastRefNo>,ILastRefNoRepository  {}
  


 public partial class meal_distribution_infoRepository : BaseRepository<Pos.Model.DbContext.meal_distribution_info>,Imeal_distribution_infoRepository  {}
  


 public partial class meal_infoRepository : BaseRepository<Pos.Model.DbContext.meal_info>,Imeal_infoRepository  {}
  


 public partial class MembAmountEditLogRepository : BaseRepository<Pos.Model.DbContext.MembAmountEditLog>,IMembAmountEditLogRepository  {}
  


 public partial class MemberRepository : BaseRepository<Pos.Model.DbContext.Member>,IMemberRepository  {}
  


 public partial class MemberCheckoutInfoRepository : BaseRepository<Pos.Model.DbContext.MemberCheckoutInfo>,IMemberCheckoutInfoRepository  {}
  


 public partial class MemberGiveSetRepository : BaseRepository<Pos.Model.DbContext.MemberGiveSet>,IMemberGiveSetRepository  {}
  


 public partial class MembSetRepository : BaseRepository<Pos.Model.DbContext.MembSet>,IMembSetRepository  {}
  


 public partial class MGradeFdDiscRepository : BaseRepository<Pos.Model.DbContext.MGradeFdDisc>,IMGradeFdDiscRepository  {}
  


 public partial class MobileFdGiveRepository : BaseRepository<Pos.Model.DbContext.MobileFdGive>,IMobileFdGiveRepository  {}
  


 public partial class MobileFoodRepository : BaseRepository<Pos.Model.DbContext.MobileFood>,IMobileFoodRepository  {}
  


 public partial class MobileFoodDiscRepository : BaseRepository<Pos.Model.DbContext.MobileFoodDisc>,IMobileFoodDiscRepository  {}
  


 public partial class MobileFtTypeRepository : BaseRepository<Pos.Model.DbContext.MobileFtType>,IMobileFtTypeRepository  {}
  


 public partial class MobilePackGiveRepository : BaseRepository<Pos.Model.DbContext.MobilePackGive>,IMobilePackGiveRepository  {}
  


 public partial class MobilOrderItemRepository : BaseRepository<Pos.Model.DbContext.MobilOrderItem>,IMobilOrderItemRepository  {}
  


 public partial class MobilOrderTitleRepository : BaseRepository<Pos.Model.DbContext.MobilOrderTitle>,IMobilOrderTitleRepository  {}
  


 public partial class MobilUserOrderTitleRepository : BaseRepository<Pos.Model.DbContext.MobilUserOrderTitle>,IMobilUserOrderTitleRepository  {}
  


 public partial class NewMemberLogRepository : BaseRepository<Pos.Model.DbContext.NewMemberLog>,INewMemberLogRepository  {}
  


 public partial class ParamSetRepository : BaseRepository<Pos.Model.DbContext.ParamSet>,IParamSetRepository  {}
  


 public partial class pre_orderRepository : BaseRepository<Pos.Model.DbContext.pre_order>,Ipre_orderRepository  {}
  


 public partial class PreOrderSendMsgInfoRepository : BaseRepository<Pos.Model.DbContext.PreOrderSendMsgInfo>,IPreOrderSendMsgInfoRepository  {}
  


 public partial class PriceNoRepository : BaseRepository<Pos.Model.DbContext.PriceNo>,IPriceNoRepository  {}
  


 public partial class QrInfoRepository : BaseRepository<Pos.Model.DbContext.QrInfo>,IQrInfoRepository  {}
  


 public partial class RecordRoomTimeRepository : BaseRepository<Pos.Model.DbContext.RecordRoomTime>,IRecordRoomTimeRepository  {}
  


 public partial class RefToZDLogRepository : BaseRepository<Pos.Model.DbContext.RefToZDLog>,IRefToZDLogRepository  {}
  


 public partial class RightSetRepository : BaseRepository<Pos.Model.DbContext.RightSet>,IRightSetRepository  {}
  


 public partial class RmAccountInfoRepository : BaseRepository<Pos.Model.DbContext.RmAccountInfo>,IRmAccountInfoRepository  {}
  


 public partial class RmAreaRepository : BaseRepository<Pos.Model.DbContext.RmArea>,IRmAreaRepository  {}
  


 public partial class RmClearLogRepository : BaseRepository<Pos.Model.DbContext.RmClearLog>,IRmClearLogRepository  {}
  


 public partial class RmCloseInfoRepository : BaseRepository<Pos.Model.DbContext.RmCloseInfo>,IRmCloseInfoRepository  {}
  


 public partial class RmCloseInfo_CollectRepository : BaseRepository<Pos.Model.DbContext.RmCloseInfo_Collect>,IRmCloseInfo_CollectRepository  {}
  


 public partial class RmExchangeDetailRepository : BaseRepository<Pos.Model.DbContext.RmExchangeDetail>,IRmExchangeDetailRepository  {}
  


 public partial class RmExchangeLogRepository : BaseRepository<Pos.Model.DbContext.RmExchangeLog>,IRmExchangeLogRepository  {}
  


 public partial class RmFtPrnIndexRepository : BaseRepository<Pos.Model.DbContext.RmFtPrnIndex>,IRmFtPrnIndexRepository  {}
  


 public partial class RmOrderRepository : BaseRepository<Pos.Model.DbContext.RmOrder>,IRmOrderRepository  {}
  


 public partial class RmOrderDelLogRepository : BaseRepository<Pos.Model.DbContext.RmOrderDelLog>,IRmOrderDelLogRepository  {}
  


 public partial class RmOrderLogRepository : BaseRepository<Pos.Model.DbContext.RmOrderLog>,IRmOrderLogRepository  {}
  


 public partial class RmsRoomRepository : BaseRepository<Pos.Model.DbContext.RmsRoom>,IRmsRoomRepository  {}
  


 public partial class RmTypeRepository : BaseRepository<Pos.Model.DbContext.RmType>,IRmTypeRepository  {}
  


 public partial class RoomRepository : BaseRepository<Pos.Model.DbContext.Room>,IRoomRepository  {}
  


 public partial class RoomCommissionRepository : BaseRepository<Pos.Model.DbContext.RoomCommission>,IRoomCommissionRepository  {}
  


 public partial class RoomExtendRepository : BaseRepository<Pos.Model.DbContext.RoomExtend>,IRoomExtendRepository  {}
  


 public partial class RoomtestRepository : BaseRepository<Pos.Model.DbContext.Roomtest>,IRoomtestRepository  {}
  


 public partial class RtAutoRepository : BaseRepository<Pos.Model.DbContext.RtAuto>,IRtAutoRepository  {}
  


 public partial class RtAutoZDRepository : BaseRepository<Pos.Model.DbContext.RtAutoZD>,IRtAutoZDRepository  {}
  


 public partial class RtTimePriceRepository : BaseRepository<Pos.Model.DbContext.RtTimePrice>,IRtTimePriceRepository  {}
  


 public partial class S_AccTypeRepository : BaseRepository<Pos.Model.DbContext.S_AccType>,IS_AccTypeRepository  {}
  


 public partial class S_CashItemRepository : BaseRepository<Pos.Model.DbContext.S_CashItem>,IS_CashItemRepository  {}
  


 public partial class S_PrnTypeRepository : BaseRepository<Pos.Model.DbContext.S_PrnType>,IS_PrnTypeRepository  {}
  


 public partial class S_RmStatusRepository : BaseRepository<Pos.Model.DbContext.S_RmStatus>,IS_RmStatusRepository  {}
  


 public partial class SchedulingRecordRepository : BaseRepository<Pos.Model.DbContext.SchedulingRecord>,ISchedulingRecordRepository  {}
  


 public partial class SDateRepository : BaseRepository<Pos.Model.DbContext.SDate>,ISDateRepository  {}
  


 public partial class ShareSetInfoRepository : BaseRepository<Pos.Model.DbContext.ShareSetInfo>,IShareSetInfoRepository  {}
  


 public partial class ShiftInfoRepository : BaseRepository<Pos.Model.DbContext.ShiftInfo>,IShiftInfoRepository  {}
  


 public partial class StarInfoRepository : BaseRepository<Pos.Model.DbContext.StarInfo>,IStarInfoRepository  {}
  


 public partial class TestTableRepository : BaseRepository<Pos.Model.DbContext.TestTable>,ITestTableRepository  {}
  


 public partial class Th_RoomCommissionAllotRepository : BaseRepository<Pos.Model.DbContext.Th_RoomCommissionAllot>,ITh_RoomCommissionAllotRepository  {}
  


 public partial class TimeZoneRepository : BaseRepository<Pos.Model.DbContext.TimeZone>,ITimeZoneRepository  {}
  


 public partial class triggerRecordRepository : BaseRepository<Pos.Model.DbContext.triggerRecord>,ItriggerRecordRepository  {}
  


 public partial class UserAmountRepository : BaseRepository<Pos.Model.DbContext.UserAmount>,IUserAmountRepository  {}
  


 public partial class UserAmountDetailRepository : BaseRepository<Pos.Model.DbContext.UserAmountDetail>,IUserAmountDetailRepository  {}
  


 public partial class UserFtZDRepository : BaseRepository<Pos.Model.DbContext.UserFtZD>,IUserFtZDRepository  {}
  


 public partial class UserIORepository : BaseRepository<Pos.Model.DbContext.UserIO>,IUserIORepository  {}
  


 public partial class UserZDItemRepository : BaseRepository<Pos.Model.DbContext.UserZDItem>,IUserZDItemRepository  {}
  


 public partial class UserZDItemDetailRepository : BaseRepository<Pos.Model.DbContext.UserZDItemDetail>,IUserZDItemDetailRepository  {}
  


 public partial class UserZDSetRepository : BaseRepository<Pos.Model.DbContext.UserZDSet>,IUserZDSetRepository  {}
  


 public partial class VesaRepository : BaseRepository<Pos.Model.DbContext.Vesa>,IVesaRepository  {}
  


 public partial class WebOrderTableRepository : BaseRepository<Pos.Model.DbContext.WebOrderTable>,IWebOrderTableRepository  {}
  


 public partial class WeChatFoodOrderMsgRepository : BaseRepository<Pos.Model.DbContext.WeChatFoodOrderMsg>,IWeChatFoodOrderMsgRepository  {}
  


 public partial class WeChatFoodOrderMsg2Repository : BaseRepository<Pos.Model.DbContext.WeChatFoodOrderMsg2>,IWeChatFoodOrderMsg2Repository  {}
  


 public partial class WindTicketRepository : BaseRepository<Pos.Model.DbContext.WindTicket>,IWindTicketRepository  {}
  


 public partial class wx_shopmall_worktimeRepository : BaseRepository<Pos.Model.DbContext.wx_shopmall_worktime>,Iwx_shopmall_worktimeRepository  {}
  


 public partial class wxPayCheckInfoRepository : BaseRepository<Pos.Model.DbContext.wxPayCheckInfo>,IwxPayCheckInfoRepository  {}
  


 public partial class wxPayInfoRepository : BaseRepository<Pos.Model.DbContext.wxPayInfo>,IwxPayInfoRepository  {}
  


}