﻿using ComponentApplicationServiceInterface.Repository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Pos.Repository
{

    public class BaseRepository<T> : RepositoryBase<T> where T : class, new()
    {

        public Pos.Model.DbContext.dbfoodEntities db = null;
        public BaseRepository()
        {
            db = DbContextFactory.CreateDbContext();
            base.dbcontext = db;



        }

    }
}
