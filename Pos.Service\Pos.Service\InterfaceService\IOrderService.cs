﻿using ComponentApplicationServiceInterface.Context.Response;
using Pos.Model.DbFood.Conntext;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Pos.Service.InterfaceService
{
    [ServiceContract]
    public interface IOrderService
    {
        [OperationContract]
        ResponseContext<bool> PlaceOrder(PlaceOrderContext model);

        [OperationContract]
        ResponseContext<int> GetOrderNumbers(GetOrderNumbersContext context);
    }
}
