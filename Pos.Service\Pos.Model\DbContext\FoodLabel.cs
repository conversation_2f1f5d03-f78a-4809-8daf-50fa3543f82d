//------------------------------------------------------------------------------
// <auto-generated>
//    此代码是根据模板生成的。
//
//    手动更改此文件可能会导致应用程序中发生异常行为。
//    如果重新生成代码，则将覆盖对此文件的手动更改。
// </auto-generated>
//------------------------------------------------------------------------------

namespace Pos.Model.DbContext
{
    using System;
    using System.Collections.Generic;
    
    public partial class FoodLabel
    {
        public int Id { get; set; }
        public string FoodCode { get; set; }
        public string FdNo { get; set; }
        public Nullable<int> RoomType { get; set; }
        public string Type { get; set; }
        public Nullable<int> PeoNumber { get; set; }
        public string Platform { get; set; }
        public string Depart { get; set; }
        public Nullable<int> BeverageType { get; set; }
        public Nullable<int> SnackType { get; set; }
        public Nullable<int> Unit { get; set; }
        public Nullable<bool> MemberMode { get; set; }
        public Nullable<int> CardType { get; set; }
        public Nullable<int> CashType { get; set; }
        public Nullable<int> HeadType { get; set; }
        public string Period { get; set; }
        public string Bank { get; set; }
        public Nullable<int> VoucherType { get; set; }
        public Nullable<int> MemberCard { get; set; }
        public Nullable<int> MemberLevel { get; set; }
        public Nullable<int> Losses { get; set; }
        public Nullable<int> PriceType { get; set; }
        public Nullable<int> BJF { get; set; }
        public Nullable<int> ServiceCharge { get; set; }
        public Nullable<int> Deduction { get; set; }
        public Nullable<int> Hours { get; set; }
        public Nullable<int> Quantity { get; set; }
        public Nullable<int> Integral { get; set; }
        public Nullable<int> PackageCost { get; set; }
        public Nullable<int> PackageType { get; set; }
        public Nullable<int> GoodsType { get; set; }
        public Nullable<decimal> DivideBar { get; set; }
        public Nullable<decimal> DivideRestaurant { get; set; }
        public Nullable<decimal> DivideInfield { get; set; }
        public Nullable<decimal> DivideBuffet { get; set; }
        public string Category1 { get; set; }
        public string Category2 { get; set; }
    }
}
