﻿



using ComponentApplicationServiceInterface.Repository;
using Pos.Model.DbContext;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Pos.Domain.IRepository
{

 public partial interface IAddItemRepository : IRepositoryBase<Pos.Model.DbContext.AddItem> {}

 public partial interface IAiTypeRepository : IRepositoryBase<Pos.Model.DbContext.AiType> {}

 public partial interface IAmountLogRepository : IRepositoryBase<Pos.Model.DbContext.AmountLog> {}

 public partial interface ICarLeaveLogRepository : IRepositoryBase<Pos.Model.DbContext.CarLeaveLog> {}

 public partial interface ICashierRepository : IRepositoryBase<Pos.Model.DbContext.Cashier> {}

 public partial interface IClearDataLogRepository : IRepositoryBase<Pos.Model.DbContext.ClearDataLog> {}

 public partial interface IDeadLockLogRepository : IRepositoryBase<Pos.Model.DbContext.DeadLockLog> {}

 public partial interface IDepositInfoRepository : IRepositoryBase<Pos.Model.DbContext.DepositInfo> {}

 public partial interface IDeptRepository : IRepositoryBase<Pos.Model.DbContext.Dept> {}

 public partial interface IDeptBanSetRepository : IRepositoryBase<Pos.Model.DbContext.DeptBanSet> {}

 public partial interface IdtpropertiesRepository : IRepositoryBase<Pos.Model.DbContext.dtproperties> {}

 public partial interface IFdCashRepository : IRepositoryBase<Pos.Model.DbContext.FdCash> {}

 public partial interface IFdCash_TrackRepository : IRepositoryBase<Pos.Model.DbContext.FdCash_Track> {}

 public partial interface IFdCashBakRepository : IRepositoryBase<Pos.Model.DbContext.FdCashBak> {}

 public partial interface IFdCashBak_BRepository : IRepositoryBase<Pos.Model.DbContext.FdCashBak_B> {}

 public partial interface IFdCashBak_BakRepository : IRepositoryBase<Pos.Model.DbContext.FdCashBak_Bak> {}

 public partial interface IFdDetTypeRepository : IRepositoryBase<Pos.Model.DbContext.FdDetType> {}

 public partial interface IFdImageRepository : IRepositoryBase<Pos.Model.DbContext.FdImage> {}

 public partial interface IFdInvRepository : IRepositoryBase<Pos.Model.DbContext.FdInv> {}

 public partial interface IFdInv_BRepository : IRepositoryBase<Pos.Model.DbContext.FdInv_B> {}

 public partial interface IFdInv_BakRepository : IRepositoryBase<Pos.Model.DbContext.FdInv_Bak> {}

 public partial interface IFdInv_ExchangeLogRepository : IRepositoryBase<Pos.Model.DbContext.FdInv_ExchangeLog> {}

 public partial interface IFdInvCashItemRepository : IRepositoryBase<Pos.Model.DbContext.FdInvCashItem> {}

 public partial interface IFdInvDescRepository : IRepositoryBase<Pos.Model.DbContext.FdInvDesc> {}

 public partial interface IFdTicketRepository : IRepositoryBase<Pos.Model.DbContext.FdTicket> {}

 public partial interface IFdTimePriceRepository : IRepositoryBase<Pos.Model.DbContext.FdTimePrice> {}

 public partial interface IFdTimeZoneRepository : IRepositoryBase<Pos.Model.DbContext.FdTimeZone> {}

 public partial interface IFdTypeRepository : IRepositoryBase<Pos.Model.DbContext.FdType> {}

 public partial interface IFdUserRepository : IRepositoryBase<Pos.Model.DbContext.FdUser> {}

 public partial interface IFdUserGradeRepository : IRepositoryBase<Pos.Model.DbContext.FdUserGrade> {}

 public partial interface IFdUserRightsRepository : IRepositoryBase<Pos.Model.DbContext.FdUserRights> {}

 public partial interface IFestivalTimeRepository : IRepositoryBase<Pos.Model.DbContext.FestivalTime> {}

 public partial interface IFoodRepository : IRepositoryBase<Pos.Model.DbContext.Food> {}

 public partial interface IFoodCalRepository : IRepositoryBase<Pos.Model.DbContext.FoodCal> {}

 public partial interface IFoodLabelRepository : IRepositoryBase<Pos.Model.DbContext.FoodLabel> {}

 public partial interface IFoodOrderMRepository : IRepositoryBase<Pos.Model.DbContext.FoodOrderM> {}

 public partial interface IFPrnRepository : IRepositoryBase<Pos.Model.DbContext.FPrn> {}

 public partial interface IFPrnDataRepository : IRepositoryBase<Pos.Model.DbContext.FPrnData> {}

 public partial interface IFPrnData_BakRepository : IRepositoryBase<Pos.Model.DbContext.FPrnData_Bak> {}

 public partial interface IFreePackageCoupon_RecordRepository : IRepositoryBase<Pos.Model.DbContext.FreePackageCoupon_Record> {}

 public partial interface IFtInfoRepository : IRepositoryBase<Pos.Model.DbContext.FtInfo> {}

 public partial interface IGDDB20InfoRepository : IRepositoryBase<Pos.Model.DbContext.GDDB20Info> {}

 public partial interface IGDDBInfoRepository : IRepositoryBase<Pos.Model.DbContext.GDDBInfo> {}

 public partial interface IHappyRabRepository : IRepositoryBase<Pos.Model.DbContext.HappyRab> {}

 public partial interface IHolidayRepository : IRepositoryBase<Pos.Model.DbContext.Holiday> {}

 public partial interface IHotFdTypeRepository : IRepositoryBase<Pos.Model.DbContext.HotFdType> {}

 public partial interface IHotFoodRepository : IRepositoryBase<Pos.Model.DbContext.HotFood> {}

 public partial interface IInv_TimeSectionRepository : IRepositoryBase<Pos.Model.DbContext.Inv_TimeSection> {}

 public partial interface IInvRollBackRepository : IRepositoryBase<Pos.Model.DbContext.InvRollBack> {}

 public partial interface ILanIdRepository : IRepositoryBase<Pos.Model.DbContext.LanId> {}

 public partial interface ILanStringRepository : IRepositoryBase<Pos.Model.DbContext.LanString> {}

 public partial interface ILastInvNoRepository : IRepositoryBase<Pos.Model.DbContext.LastInvNo> {}

 public partial interface ILastRefNoRepository : IRepositoryBase<Pos.Model.DbContext.LastRefNo> {}

 public partial interface Imeal_distribution_infoRepository : IRepositoryBase<Pos.Model.DbContext.meal_distribution_info> {}

 public partial interface Imeal_infoRepository : IRepositoryBase<Pos.Model.DbContext.meal_info> {}

 public partial interface IMembAmountEditLogRepository : IRepositoryBase<Pos.Model.DbContext.MembAmountEditLog> {}

 public partial interface IMemberRepository : IRepositoryBase<Pos.Model.DbContext.Member> {}

 public partial interface IMemberCheckoutInfoRepository : IRepositoryBase<Pos.Model.DbContext.MemberCheckoutInfo> {}

 public partial interface IMemberGiveSetRepository : IRepositoryBase<Pos.Model.DbContext.MemberGiveSet> {}

 public partial interface IMembSetRepository : IRepositoryBase<Pos.Model.DbContext.MembSet> {}

 public partial interface IMGradeFdDiscRepository : IRepositoryBase<Pos.Model.DbContext.MGradeFdDisc> {}

 public partial interface IMobileFdGiveRepository : IRepositoryBase<Pos.Model.DbContext.MobileFdGive> {}

 public partial interface IMobileFoodRepository : IRepositoryBase<Pos.Model.DbContext.MobileFood> {}

 public partial interface IMobileFoodDiscRepository : IRepositoryBase<Pos.Model.DbContext.MobileFoodDisc> {}

 public partial interface IMobileFtTypeRepository : IRepositoryBase<Pos.Model.DbContext.MobileFtType> {}

 public partial interface IMobilePackGiveRepository : IRepositoryBase<Pos.Model.DbContext.MobilePackGive> {}

 public partial interface IMobilOrderItemRepository : IRepositoryBase<Pos.Model.DbContext.MobilOrderItem> {}

 public partial interface IMobilOrderTitleRepository : IRepositoryBase<Pos.Model.DbContext.MobilOrderTitle> {}

 public partial interface IMobilUserOrderTitleRepository : IRepositoryBase<Pos.Model.DbContext.MobilUserOrderTitle> {}

 public partial interface INewMemberLogRepository : IRepositoryBase<Pos.Model.DbContext.NewMemberLog> {}

 public partial interface IParamSetRepository : IRepositoryBase<Pos.Model.DbContext.ParamSet> {}

 public partial interface Ipre_orderRepository : IRepositoryBase<Pos.Model.DbContext.pre_order> {}

 public partial interface IPreOrderSendMsgInfoRepository : IRepositoryBase<Pos.Model.DbContext.PreOrderSendMsgInfo> {}

 public partial interface IPriceNoRepository : IRepositoryBase<Pos.Model.DbContext.PriceNo> {}

 public partial interface IQrInfoRepository : IRepositoryBase<Pos.Model.DbContext.QrInfo> {}

 public partial interface IRecordRoomTimeRepository : IRepositoryBase<Pos.Model.DbContext.RecordRoomTime> {}

 public partial interface IRefToZDLogRepository : IRepositoryBase<Pos.Model.DbContext.RefToZDLog> {}

 public partial interface IRightSetRepository : IRepositoryBase<Pos.Model.DbContext.RightSet> {}

 public partial interface IRmAccountInfoRepository : IRepositoryBase<Pos.Model.DbContext.RmAccountInfo> {}

 public partial interface IRmAreaRepository : IRepositoryBase<Pos.Model.DbContext.RmArea> {}

 public partial interface IRmClearLogRepository : IRepositoryBase<Pos.Model.DbContext.RmClearLog> {}

 public partial interface IRmCloseInfoRepository : IRepositoryBase<Pos.Model.DbContext.RmCloseInfo> {}

 public partial interface IRmCloseInfo_CollectRepository : IRepositoryBase<Pos.Model.DbContext.RmCloseInfo_Collect> {}

 public partial interface IRmExchangeDetailRepository : IRepositoryBase<Pos.Model.DbContext.RmExchangeDetail> {}

 public partial interface IRmExchangeLogRepository : IRepositoryBase<Pos.Model.DbContext.RmExchangeLog> {}

 public partial interface IRmFtPrnIndexRepository : IRepositoryBase<Pos.Model.DbContext.RmFtPrnIndex> {}

 public partial interface IRmOrderRepository : IRepositoryBase<Pos.Model.DbContext.RmOrder> {}

 public partial interface IRmOrderDelLogRepository : IRepositoryBase<Pos.Model.DbContext.RmOrderDelLog> {}

 public partial interface IRmOrderLogRepository : IRepositoryBase<Pos.Model.DbContext.RmOrderLog> {}

 public partial interface IRmsRoomRepository : IRepositoryBase<Pos.Model.DbContext.RmsRoom> {}

 public partial interface IRmTypeRepository : IRepositoryBase<Pos.Model.DbContext.RmType> {}

 public partial interface IRoomRepository : IRepositoryBase<Pos.Model.DbContext.Room> {}

 public partial interface IRoomCommissionRepository : IRepositoryBase<Pos.Model.DbContext.RoomCommission> {}

 public partial interface IRoomExtendRepository : IRepositoryBase<Pos.Model.DbContext.RoomExtend> {}

 public partial interface IRoomtestRepository : IRepositoryBase<Pos.Model.DbContext.Roomtest> {}

 public partial interface IRtAutoRepository : IRepositoryBase<Pos.Model.DbContext.RtAuto> {}

 public partial interface IRtAutoZDRepository : IRepositoryBase<Pos.Model.DbContext.RtAutoZD> {}

 public partial interface IRtTimePriceRepository : IRepositoryBase<Pos.Model.DbContext.RtTimePrice> {}

 public partial interface IS_AccTypeRepository : IRepositoryBase<Pos.Model.DbContext.S_AccType> {}

 public partial interface IS_CashItemRepository : IRepositoryBase<Pos.Model.DbContext.S_CashItem> {}

 public partial interface IS_PrnTypeRepository : IRepositoryBase<Pos.Model.DbContext.S_PrnType> {}

 public partial interface IS_RmStatusRepository : IRepositoryBase<Pos.Model.DbContext.S_RmStatus> {}

 public partial interface ISchedulingRecordRepository : IRepositoryBase<Pos.Model.DbContext.SchedulingRecord> {}

 public partial interface ISDateRepository : IRepositoryBase<Pos.Model.DbContext.SDate> {}

 public partial interface IShareSetInfoRepository : IRepositoryBase<Pos.Model.DbContext.ShareSetInfo> {}

 public partial interface IShiftInfoRepository : IRepositoryBase<Pos.Model.DbContext.ShiftInfo> {}

 public partial interface IStarInfoRepository : IRepositoryBase<Pos.Model.DbContext.StarInfo> {}

 public partial interface ITestTableRepository : IRepositoryBase<Pos.Model.DbContext.TestTable> {}

 public partial interface ITh_RoomCommissionAllotRepository : IRepositoryBase<Pos.Model.DbContext.Th_RoomCommissionAllot> {}

 public partial interface ITimeZoneRepository : IRepositoryBase<Pos.Model.DbContext.TimeZone> {}

 public partial interface ItriggerRecordRepository : IRepositoryBase<Pos.Model.DbContext.triggerRecord> {}

 public partial interface IUserAmountRepository : IRepositoryBase<Pos.Model.DbContext.UserAmount> {}

 public partial interface IUserAmountDetailRepository : IRepositoryBase<Pos.Model.DbContext.UserAmountDetail> {}

 public partial interface IUserFtZDRepository : IRepositoryBase<Pos.Model.DbContext.UserFtZD> {}

 public partial interface IUserIORepository : IRepositoryBase<Pos.Model.DbContext.UserIO> {}

 public partial interface IUserZDItemRepository : IRepositoryBase<Pos.Model.DbContext.UserZDItem> {}

 public partial interface IUserZDItemDetailRepository : IRepositoryBase<Pos.Model.DbContext.UserZDItemDetail> {}

 public partial interface IUserZDSetRepository : IRepositoryBase<Pos.Model.DbContext.UserZDSet> {}

 public partial interface IVesaRepository : IRepositoryBase<Pos.Model.DbContext.Vesa> {}

 public partial interface IWebOrderTableRepository : IRepositoryBase<Pos.Model.DbContext.WebOrderTable> {}

 public partial interface IWeChatFoodOrderMsgRepository : IRepositoryBase<Pos.Model.DbContext.WeChatFoodOrderMsg> {}

 public partial interface IWeChatFoodOrderMsg2Repository : IRepositoryBase<Pos.Model.DbContext.WeChatFoodOrderMsg2> {}

 public partial interface IWindTicketRepository : IRepositoryBase<Pos.Model.DbContext.WindTicket> {}

 public partial interface Iwx_shopmall_worktimeRepository : IRepositoryBase<Pos.Model.DbContext.wx_shopmall_worktime> {}

 public partial interface IwxPayCheckInfoRepository : IRepositoryBase<Pos.Model.DbContext.wxPayCheckInfo> {}

 public partial interface IwxPayInfoRepository : IRepositoryBase<Pos.Model.DbContext.wxPayInfo> {}

}
