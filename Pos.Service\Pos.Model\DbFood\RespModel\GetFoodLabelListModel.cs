﻿using Pos.Model.DbContext;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Pos.Model.DbFood.RespModel
{
    public class GetFoodLabelListModel
    {
        public string FdNo { get; set; }

        public string FoodCode { get; set; }

        public string FdCName { get; set; }

        public string FtCName { get; set; }

        public string Type { get; set; }

        public Nullable<decimal> DivideBar { get; set; }

        public Nullable<decimal> DivideRestaurant { get; set; }

        public Nullable<decimal> DivideInfield { get; set; }

        public Nullable<decimal> DivideBuffet { get; set; }

        public Nullable<int> PeoNumber { get; set; }
    }

    public class GetFoodLabelInfoModel 
    {
        public string FdCName { get; set; }

        public string FtCName { get; set; }

        public FoodLabel FoodLabel { get; set; }

        public int TypeId { get; set; }

        public int CateId { get; set; }
    }
}
