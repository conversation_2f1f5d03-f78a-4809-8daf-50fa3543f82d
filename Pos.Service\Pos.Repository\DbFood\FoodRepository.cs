﻿using Pos.Domain.IRepository;
using Pos.Model.DbFood.Context;
using Pos.Model.DbFood.RespModel;
using System;
using System.Collections.Generic;
using System.Data.Objects.SqlClient;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace Pos.Repository
{
    public partial class FoodRepository : BaseRepository<Model.DbContext.Food>, IFoodRepository
    {
        public List<GetStoreFtTypeReportModel> GetDetailReport(GetStoreFtTypeReportContext context)
        {
            var sql = @"SELECT a.InvNo,a.FdNo,a.FdCName,a.FdPrice,a.FdQty,a.CashTime,a.CashType,a.CashUserName,b.* FROM [dbo].[FdCashBak] as a
                        JOIN FoodLabel as b on a.FdNo = b.FdNo JOIN Food as c ON a.FdNo = c.FdNo JOIN FdType as d on c.FtNo = d.FtNo JOIN FdInv AS e ON a.InvNo = e.InvNo WHERE d.FtNo = @FtNo AND e.WorkDate >= @StartTime AND e.WorkDate <= @EndTime";

            var param = new List<SqlParameter>()
            {
                new SqlParameter("@FtNo",context.FtType),
                new SqlParameter("@StartTime",context.StartTime.ToString("yyyyMMdd")),
                new SqlParameter("@EndTime",context.EndTime.ToString("yyyyMMdd")),
            };

            var list = dbcontext.Database.SqlQuery<GetStoreFtTypeReportModel>(sql, param.ToArray()).ToList();

            return list;
        }

        public List<HeadCountModel> GetHeadCountReport(GetStoreReportContext context)
        {
            string sql = @"SELECT lable.Category1,lable.Category2,(lable.PeoNumber * item.FdQty) as PeoNumber,lable.Type
                            FROM FdCashBak AS item
                            JOIN FdInv AS fd ON item.InvNo = fd.InvNo
                            JOIN Food AS food ON item.FdNo = food.FdNo
                            JOIN FdType AS fdtype ON food.FtNo = fdtype.FtNo
                            LEFT JOIN FoodLabel AS lable ON item.FdNo = lable.FdNo
                            WHERE lable.PeoNumber > 0 AND item.CashType = 'N'
                            AND fd.WorkDate >= @StartTime AND fd.WorkDate <= @EndTime";

            var param = new List<SqlParameter>()
            {
                new SqlParameter("@StartTime",context.StartTime.ToString("yyyyMMdd")),
                new SqlParameter("@EndTime",context.EndTime.ToString("yyyyMMdd")),
            };

            var data = dbcontext.Database.SqlQuery<HeadCountModel>(sql, param.ToArray()).ToList();
            return data;
        }

        /// <summary>
        /// 成本核算
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<StoreReportModel> GetStoreReport(GetStoreReportContext context)
        {
            string sql = @"SELECT
	                        a.invno AS InvNo,
	                        a.fdno AS FdNo,
	                        a.fdcname AS FdCName,
	                        fdprice AS FdPrice,
	                        a.fdqty AS FdQty,
	                        a.cashtype AS CashType,
	                        a.cashtime AS CashTime,
	                        CASE WHEN a.CashUserName IS NULL THEN '' ELSE a.CashUserName END CashUserName,
	                        b.workdate AS WorkDate,
                            CASE WHEN ( ( SELECT MAX ( ShareFdNo ) FROM sharesetinfo WHERE ShareFdNo = a.FdNo ) IS NULL ) THEN 0 ELSE 1 
	                        END AS IsPackage,
	                        d.FtNo AS FtNo,
	                        d.FtCName AS FtCName,
	                        d.PrnType AS PrnType
                            FROM fdcashbak AS a
	                        JOIN fdinv AS b ON a.invno= b.invno
	                        JOIN Food AS c ON a.FdNo = c.FdNo
	                        JOIN FdType AS d ON c.FtNo = d.FtNo
                            WHERE workdate >=@StartTime 
	                        AND WorkDate <= @EndTime
                            AND a.CashType != 'X'";

            var param = new List<SqlParameter>()
            {
                new SqlParameter("@StartTime",context.StartTime.ToString("yyyyMMdd")),
                new SqlParameter("@EndTime",context.EndTime.ToString("yyyyMMdd")),
            };

            //查出所有数据
            var data = dbcontext.Database.SqlQuery<StoreReportModel>(sql, param.ToArray()).ToList();
            return data;
        }

        public List<WaitCreateModel> GetWaitCreate()
        {
            var data = (from food in db.Food
                        join label in db.FoodLabel on food.FdNo equals label.FdNo into intoData
                        from da in intoData.DefaultIfEmpty()
                        where da == null
                        select new { food.FdNo, food.FdCName }).ToList();

            var result = data.Select(x => new WaitCreateModel()
            {
                FdNo = x.FdNo,
                FdCName = x.FdCName,
            }).ToList();

            return result;
        }

        public GetFoodModel GetFood(string fdNo)
        {
            var sql = "Select FdNo,FdCName from Food where FdNo = @FdNo";

            var param = new List<SqlParameter>()
            {
                new SqlParameter("@FdNo",fdNo),
            };

            return dbcontext.Database.SqlQuery<GetFoodModel>(sql, param.ToArray()).FirstOrDefault();
        }
    }
}
