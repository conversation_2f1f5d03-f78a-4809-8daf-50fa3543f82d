﻿using Pos.Model.DbContext;
using Pos.Model.DbFood.Conntext;
using Pos.Model.DbFood.RespModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Pos.Application
{
    public partial class FoodLabelApp : AppBase<FoodLabel>
    {
        public List<GetFoodLabelListModel> GetFoodLabelList(FoodLabelContext context)
        {
            var query = from food in Repository.Food.IQueryable().AsEnumerable()
                        join type in Repository.FdType.IQueryable().AsEnumerable() on food.FtNo equals type.FtNo
                        join label in Repository.FoodLabel.IQueryable().AsEnumerable() on food.FdNo equals label.FdNo into labelDefault
                        from leftLabel in labelDefault.DefaultIfEmpty()
                        select new
                        {
                            FtNo = type.FtNo,
                            FdNo = food.FdNo,
                            FtCName = type.FtCName,
                            FdCName = food.FdCName,
                            Type = leftLabel == null ? string.Empty : leftLabel.Type,
                            FoodCode = leftLabel == null ? string.Empty : leftLabel.FoodCode,
                            DivideBar = leftLabel == null ? 0 : leftLabel.DivideBar,
                            DivideRestaurant = leftLabel == null ? 0 : leftLabel.DivideRestaurant,
                            DivideInfield = leftLabel == null ? 0 : leftLabel.DivideInfield,
                            DivideBuffet = leftLabel == null ? 0 : leftLabel.DivideBuffet,
                            PeoNumber = leftLabel == null ? 0 : leftLabel.PeoNumber
                        };

            if (!string.IsNullOrEmpty(context.FtNo))
                query = query.Where(w => w.FtNo == context.FtNo);
            //改成可以用名字搜索也可以用食品编号搜索
            if (!string.IsNullOrEmpty(context.SearchKey))
                query = query.Where(w => w.FdCName.Contains(context.SearchKey) || w.FdNo.Contains(context.SearchKey));
            //是否存在标签，有标签表数据一定会有编号，没有标签表数据一定没有编号
            if (context.HasLabel.HasValue)
            {
                if (context.HasLabel.Value)
                    query = query.Where(w => !string.IsNullOrEmpty(w.FoodCode));
                else
                    query = query.Where(w => string.IsNullOrEmpty(w.FoodCode));
            }

            context.pagination.records = query.Count();
            var list = query.OrderBy(w => w.FdNo).Skip(context.pagination.rows * (context.pagination.page - 1)).Take(context.pagination.rows).Select(x => new GetFoodLabelListModel()
            {
                FdNo = x.FdNo,
                FoodCode = x.FoodCode,
                FdCName = x.FdCName,
                FtCName = x.FtCName,
                Type = x.Type,
                DivideBar = x.DivideBar,
                DivideBuffet = x.DivideBuffet,
                DivideInfield = x.DivideInfield,
                DivideRestaurant = x.DivideRestaurant,
                PeoNumber = x.PeoNumber
            }).ToList();

            return list;
        }

        public override int Insert(List<FoodLabel> entities)
        {
            return Repository.FoodLabel.Insert(entities);
        }

        public int GetMaxCode()
        {
            return Repository.FoodLabel.GetMaxCode();
        }

    }
}
