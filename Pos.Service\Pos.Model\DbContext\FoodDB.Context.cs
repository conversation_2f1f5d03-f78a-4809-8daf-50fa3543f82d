﻿//------------------------------------------------------------------------------
// <auto-generated>
//    此代码是根据模板生成的。
//
//    手动更改此文件可能会导致应用程序中发生异常行为。
//    如果重新生成代码，则将覆盖对此文件的手动更改。
// </auto-generated>
//------------------------------------------------------------------------------

namespace Pos.Model.DbContext
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    
    public partial class dbfoodEntities : DbContext
    {
        public dbfoodEntities()
            : base("name=dbfoodEntities")
        {
        }
    
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }
    
        public DbSet<AddItem> AddItem { get; set; }
        public DbSet<AiType> AiType { get; set; }
        public DbSet<AmountLog> AmountLog { get; set; }
        public DbSet<CarLeaveLog> CarLeaveLog { get; set; }
        public DbSet<Cashier> Cashier { get; set; }
        public DbSet<ClearDataLog> ClearDataLog { get; set; }
        public DbSet<DeadLockLog> DeadLockLog { get; set; }
        public DbSet<DepositInfo> DepositInfo { get; set; }
        public DbSet<Dept> Dept { get; set; }
        public DbSet<DeptBanSet> DeptBanSet { get; set; }
        public DbSet<dtproperties> dtproperties { get; set; }
        public DbSet<FdCash> FdCash { get; set; }
        public DbSet<FdCash_Track> FdCash_Track { get; set; }
        public DbSet<FdCashBak> FdCashBak { get; set; }
        public DbSet<FdCashBak_B> FdCashBak_B { get; set; }
        public DbSet<FdCashBak_Bak> FdCashBak_Bak { get; set; }
        public DbSet<FdDetType> FdDetType { get; set; }
        public DbSet<FdImage> FdImage { get; set; }
        public DbSet<FdInv> FdInv { get; set; }
        public DbSet<FdInv_B> FdInv_B { get; set; }
        public DbSet<FdInv_Bak> FdInv_Bak { get; set; }
        public DbSet<FdInv_ExchangeLog> FdInv_ExchangeLog { get; set; }
        public DbSet<FdInvCashItem> FdInvCashItem { get; set; }
        public DbSet<FdInvDesc> FdInvDesc { get; set; }
        public DbSet<FdTicket> FdTicket { get; set; }
        public DbSet<FdTimePrice> FdTimePrice { get; set; }
        public DbSet<FdTimeZone> FdTimeZone { get; set; }
        public DbSet<FdType> FdType { get; set; }
        public DbSet<FdUser> FdUser { get; set; }
        public DbSet<FdUserGrade> FdUserGrade { get; set; }
        public DbSet<FdUserRights> FdUserRights { get; set; }
        public DbSet<FestivalTime> FestivalTime { get; set; }
        public DbSet<Food> Food { get; set; }
        public DbSet<FoodCal> FoodCal { get; set; }
        public DbSet<FoodOrderM> FoodOrderM { get; set; }
        public DbSet<FPrn> FPrn { get; set; }
        public DbSet<FPrnData> FPrnData { get; set; }
        public DbSet<FPrnData_Bak> FPrnData_Bak { get; set; }
        public DbSet<FreePackageCoupon_Record> FreePackageCoupon_Record { get; set; }
        public DbSet<FtInfo> FtInfo { get; set; }
        public DbSet<GDDB20Info> GDDB20Info { get; set; }
        public DbSet<GDDBInfo> GDDBInfo { get; set; }
        public DbSet<HappyRab> HappyRab { get; set; }
        public DbSet<Holiday> Holiday { get; set; }
        public DbSet<HotFdType> HotFdType { get; set; }
        public DbSet<HotFood> HotFood { get; set; }
        public DbSet<Inv_TimeSection> Inv_TimeSection { get; set; }
        public DbSet<InvRollBack> InvRollBack { get; set; }
        public DbSet<LanId> LanId { get; set; }
        public DbSet<LanString> LanString { get; set; }
        public DbSet<LastInvNo> LastInvNo { get; set; }
        public DbSet<LastRefNo> LastRefNo { get; set; }
        public DbSet<meal_distribution_info> meal_distribution_info { get; set; }
        public DbSet<meal_info> meal_info { get; set; }
        public DbSet<MembAmountEditLog> MembAmountEditLog { get; set; }
        public DbSet<Member> Member { get; set; }
        public DbSet<MemberCheckoutInfo> MemberCheckoutInfo { get; set; }
        public DbSet<MemberGiveSet> MemberGiveSet { get; set; }
        public DbSet<MembSet> MembSet { get; set; }
        public DbSet<MGradeFdDisc> MGradeFdDisc { get; set; }
        public DbSet<MobileFdGive> MobileFdGive { get; set; }
        public DbSet<MobileFood> MobileFood { get; set; }
        public DbSet<MobileFoodDisc> MobileFoodDisc { get; set; }
        public DbSet<MobileFtType> MobileFtType { get; set; }
        public DbSet<MobilePackGive> MobilePackGive { get; set; }
        public DbSet<MobilOrderItem> MobilOrderItem { get; set; }
        public DbSet<MobilOrderTitle> MobilOrderTitle { get; set; }
        public DbSet<MobilUserOrderTitle> MobilUserOrderTitle { get; set; }
        public DbSet<NewMemberLog> NewMemberLog { get; set; }
        public DbSet<ParamSet> ParamSet { get; set; }
        public DbSet<pre_order> pre_order { get; set; }
        public DbSet<PreOrderSendMsgInfo> PreOrderSendMsgInfo { get; set; }
        public DbSet<PriceNo> PriceNo { get; set; }
        public DbSet<QrInfo> QrInfo { get; set; }
        public DbSet<RecordRoomTime> RecordRoomTime { get; set; }
        public DbSet<RefToZDLog> RefToZDLog { get; set; }
        public DbSet<RightSet> RightSet { get; set; }
        public DbSet<RmAccountInfo> RmAccountInfo { get; set; }
        public DbSet<RmArea> RmArea { get; set; }
        public DbSet<RmClearLog> RmClearLog { get; set; }
        public DbSet<RmCloseInfo> RmCloseInfo { get; set; }
        public DbSet<RmCloseInfo_Collect> RmCloseInfo_Collect { get; set; }
        public DbSet<RmExchangeDetail> RmExchangeDetail { get; set; }
        public DbSet<RmExchangeLog> RmExchangeLog { get; set; }
        public DbSet<RmFtPrnIndex> RmFtPrnIndex { get; set; }
        public DbSet<RmOrder> RmOrder { get; set; }
        public DbSet<RmOrderDelLog> RmOrderDelLog { get; set; }
        public DbSet<RmOrderLog> RmOrderLog { get; set; }
        public DbSet<RmsRoom> RmsRoom { get; set; }
        public DbSet<RmType> RmType { get; set; }
        public DbSet<Room> Room { get; set; }
        public DbSet<RoomCommission> RoomCommission { get; set; }
        public DbSet<RoomExtend> RoomExtend { get; set; }
        public DbSet<Roomtest> Roomtest { get; set; }
        public DbSet<RtAuto> RtAuto { get; set; }
        public DbSet<RtAutoZD> RtAutoZD { get; set; }
        public DbSet<RtTimePrice> RtTimePrice { get; set; }
        public DbSet<S_AccType> S_AccType { get; set; }
        public DbSet<S_CashItem> S_CashItem { get; set; }
        public DbSet<S_PrnType> S_PrnType { get; set; }
        public DbSet<S_RmStatus> S_RmStatus { get; set; }
        public DbSet<SchedulingRecord> SchedulingRecord { get; set; }
        public DbSet<SDate> SDate { get; set; }
        public DbSet<ShareSetInfo> ShareSetInfo { get; set; }
        public DbSet<ShiftInfo> ShiftInfo { get; set; }
        public DbSet<StarInfo> StarInfo { get; set; }
        public DbSet<TestTable> TestTable { get; set; }
        public DbSet<Th_RoomCommissionAllot> Th_RoomCommissionAllot { get; set; }
        public DbSet<TimeZone> TimeZone { get; set; }
        public DbSet<triggerRecord> triggerRecord { get; set; }
        public DbSet<UserAmount> UserAmount { get; set; }
        public DbSet<UserAmountDetail> UserAmountDetail { get; set; }
        public DbSet<UserFtZD> UserFtZD { get; set; }
        public DbSet<UserIO> UserIO { get; set; }
        public DbSet<UserZDItem> UserZDItem { get; set; }
        public DbSet<UserZDItemDetail> UserZDItemDetail { get; set; }
        public DbSet<UserZDSet> UserZDSet { get; set; }
        public DbSet<Vesa> Vesa { get; set; }
        public DbSet<WebOrderTable> WebOrderTable { get; set; }
        public DbSet<WeChatFoodOrderMsg> WeChatFoodOrderMsg { get; set; }
        public DbSet<WeChatFoodOrderMsg2> WeChatFoodOrderMsg2 { get; set; }
        public DbSet<WindTicket> WindTicket { get; set; }
        public DbSet<wx_shopmall_worktime> wx_shopmall_worktime { get; set; }
        public DbSet<wxPayCheckInfo> wxPayCheckInfo { get; set; }
        public DbSet<wxPayInfo> wxPayInfo { get; set; }
        public DbSet<FoodLabel> FoodLabel { get; set; }
    }
}
