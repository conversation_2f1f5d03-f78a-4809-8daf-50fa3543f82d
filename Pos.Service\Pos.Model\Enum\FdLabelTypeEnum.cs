﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;

namespace Pos.Model.Enum
{
    /// <summary>
    /// 食品标签类型枚举
    /// </summary>
    public enum FdLabelTypeEnum
    {
        /// <summary>
        /// 纯自助餐-半价
        /// </summary>
        [Description("纯自助餐-半价")]
        Buffet_BJ = 1,

        /// <summary>
        /// 纯自助餐-正价
        /// </summary>
        [Description("纯自助餐-正价")]
        Buffet_ZJ = 2,

        /// <summary>
        /// 纯自助餐-直落
        /// </summary>
        [Description("纯自助餐-直落")]
        Buffet_ZL = 3,

        /// <summary>
        /// K+自助餐-半价
        /// </summary>
        [Description("K+自助餐-半价")]
        SingAndBuffet_BJ = 4,

        /// <summary>
        /// K+自助餐-正价
        /// </summary>
        [Description("K+自助餐-正价")]
        SingAndBuffet_ZJ = 5,

        /// <summary>
        /// K+自助餐-直落
        /// </summary>
        [Description("K+自助餐-直落")]
        SingAndBuffet_ZL = 6,

        /// <summary>
        /// 非会员价K+自助餐-半价
        /// </summary>
        [Description("非会员价K+自助餐-半价")]
        NonMemberKAndBuffet_BJ = 7,

        /// <summary>
        /// 非会员价K+自助餐-正价
        /// </summary>
        [Description("非会员价K+自助餐-正价")]
        NonMemberKAndBuffet_ZJ = 8,

        /// <summary>
        /// 非会员价K+自助餐-直落
        /// </summary>
        [Description("非会员价K+自助餐-直落")]
        NonMemberKAndBuffet_ZL = 9,

        /// <summary>
        /// K+自助餐团购-口碑
        /// </summary>
        [Description("K+自助餐团购-口碑")]
        SingAndBuffetGroup_KB = 10,

        /// <summary>
        /// K+自助餐团购-公众号
        /// </summary>
        [Description("K+自助餐团购-公众号")]
        SingAndBuffetGroup_GZH = 11,

        /// <summary>
        /// K+自助餐团购-支付宝
        /// </summary>
        [Description("K+自助餐团购-支付宝")]
        SingAndBuffetGroup_ZFB = 12,

        /// <summary>
        /// K+自助餐团购-会员购
        /// </summary>
        [Description("K+自助餐团购-会员购")]
        SingAndBuffetGroup_HYG = 13,

        /// <summary>
        /// K+自助餐团购-在线预订
        /// </summary>
        [Description("K+自助餐团购-在线预订")]
        SingAndBuffetGroup_ZXYD = 14,

        /// <summary>
        /// K+自助餐团购-抖音
        /// </summary>
        [Description("K+自助餐团购-抖音")]
        SingAndBuffetGroup_DY = 15,

        /// <summary>
        /// K+自助餐团购-贪吃
        /// </summary>
        [Description("K+自助餐团购-贪吃")]
        SingAndBuffetGroup_TC = 16,

        /// <summary>
        /// K+自助餐团购-剧本杀
        /// </summary>
        [Description("K+自助餐团购-剧本杀")]
        SingAndBuffetGroup_JBS = 17,

        /// <summary>
        /// K+自助餐团购-旅划算
        /// </summary>
        [Description("K+自助餐团购-旅划算")]
        SingAndBuffetGroup_LHS = 18,

        /// <summary>
        /// K+自助餐团购-新美大
        /// </summary>
        [Description("K+自助餐团购-新美大")]
        SingAndBuffetGroup_XMD = 19,

        /// <summary>
        /// K+自助餐银行活动-广日银联
        /// </summary>
        [Description("K+自助餐银行活动-广日银联")]
        SingAndBuffetBank_GRYL = 20,

        /// <summary>
        /// K+自助餐银行活动-广发
        /// </summary>
        [Description("K+自助餐银行活动-广发")]
        SingAndBuffetBank_GF = 21,

        /// <summary>
        /// K+自助餐银行活动-中行
        /// </summary>
        [Description("K+自助餐银行活动-中行")]
        SingAndBuffetBank_ZH = 22,

        /// <summary>
        /// K+自助餐银行活动-中信
        /// </summary>
        [Description("K+自助餐银行活动-中信")]
        SingAndBuffetBank_ZX = 23,

        /// <summary>
        /// K+自助餐银行活动-平安
        /// </summary>
        [Description("K+自助餐银行活动-平安")]
        SingAndBuffetBank_PA = 24,

        /// <summary>
        /// K+自助餐银行活动-交行
        /// </summary>
        [Description("K+自助餐银行活动-交行")]
        SingAndBuffetBank_JH = 25,

        /// <summary>
        /// K+自助餐银行活动-光大
        /// </summary>
        [Description("K+自助餐银行活动-光大")]
        SingAndBuffetBank_GD = 26,

        /// <summary>
        /// K+自助餐银行活动-兴业
        /// </summary>
        [Description("K+自助餐银行活动-兴业")]
        SingAndBuffetBank_XY = 27,

        /// <summary>
        /// K+自助餐银行活动-建设
        /// </summary>
        [Description("K+自助餐银行活动-建设")]
        SingAndBuffetBank_JS = 28,

        /// <summary>
        /// 套餐团购-公众号
        /// </summary>
        [Description("套餐团购-公众号")]
        PackageGroup_GZH = 29,

        /// <summary>
        /// 套餐团购-抖音
        /// </summary>
        [Description("套餐团购-抖音")]
        PackageGroup_DY = 30,

        /// <summary>
        /// 套餐团购-剧本杀
        /// </summary>
        [Description("套餐团购-剧本杀")]
        PackageGroup_JBS = 31,

        /// <summary>
        /// 套餐团购-新美大
        /// </summary>
        [Description("套餐团购-新美大")]
        PackageGroup_XMD = 32,

        /// <summary>
        /// 赠送类-3免1
        /// </summary>
        [Description("赠送类-3免1")]
        Give_Three = 33,

        /// <summary>
        /// 赠送类-4免1
        /// </summary>
        [Description("赠送类-4免1")]
        Give_Four = 34,

        /// <summary>
        /// 赠送类-一位全免
        /// </summary>
        [Description("赠送类-一位全免")]
        Give_One = 35,

        /// <summary>
        /// 赠送类-充值反赠
        /// </summary>
        [Description("赠送类-充值返赠")]
        Give_CZFZ = 36,

        /// <summary>
        /// 赠送类-员工欢唱券
        /// </summary>
        [Description("赠送类-员工欢唱券")]
        Give_YGHCQ = 37,

        /// <summary>
        /// 赠送类-其他
        /// </summary>
        [Description("赠送类-其他")]
        Give_QT = 38,

        /// <summary>
        /// 内场
        /// </summary>
        [Description("内场")]
        Infield = 39,

        /// <summary>
        /// 内场-杂收
        /// </summary>
        [Description("内场-杂收")]
        Infield_ZS = 40,

        /// <summary>
        /// 团购补差
        /// </summary>
        [Description("团购补差")]
        Group_BC = 41,

        /// <summary>
        /// 在线预订
        /// </summary>
        [Description("在线预订")]
        Online = 42,

        /// <summary>
        /// 其他
        /// </summary>
        [Description("其他")]
        Other = 43,

        /// <summary>
        /// 夜班小吃
        /// </summary>
        [Description("夜班小吃")]
        Restaurant = 44,

        /// <summary>
        /// 房费银行活动
        /// </summary>
        [Description("房费银行活动")]
        RoomFeeBank = 45,

        /// <summary>
        /// 酒吧
        /// </summary>
        [Description("酒吧")]
        Bar = 46,

        /// <summary>
        /// 预收
        /// </summary>
        [Description("预收")]
        CollectAdvance = 47,

        /// <summary>
        /// 厨房
        /// </summary>
        [Description("厨房")]
        Kitchen = 48,
    }
}
