﻿using Pos.Model.DbFood.RespModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Pos.Common
{
    /// <summary>
    /// 计算扩展类
    /// </summary>
    public static class ComputationalExtend
    {
        /// <summary>
        /// 通过分成公式计算返回指定类型
        /// </summary>
        /// <param name="unifieds">数据源</param>
        /// <returns></returns>
        public static List<GetStoreReportModel> DividedCalculate(this List<UnifiedComputeModel> unifieds)
        {
            var result = unifieds.GroupBy(w => w.FtNo).Select(type =>
            {
                decimal barDivision = 0;//酒吧总和
                decimal buffetDivision = 0;//自助餐总和
                decimal vightDivision = 0;//夜场总和
                decimal infieldDivision = 0;//内场总和

                type.ToList().ForEach(w =>
                {
                    if (w.Package != null)
                    {
                        var tuple = PackageCompute(w.TotalPrice, w.Package);
                        vightDivision += tuple.Item1;
                        barDivision += tuple.Item2;
                        infieldDivision += tuple.Item3;
                    }
                    else
                    {
                        barDivision += w.TotalPrice * w.BarDivision;
                        buffetDivision += w.TotalPrice * w.BuffetDivision;
                        vightDivision += w.TotalPrice * w.NightDivision;
                        infieldDivision += w.TotalPrice * w.InfieldDivision;
                    }
                });

                var data = new GetStoreReportModel()
                {
                    FtNo = type.Key,
                    FtCName = type.FirstOrDefault().FtName,
                    TotalAmount = buffetDivision + barDivision + infieldDivision + vightDivision,
                    BuffetDivision = buffetDivision,
                    BarDivision = barDivision,
                    InfieldDivision = infieldDivision,
                    NightDivision = vightDivision
                };

                return data;
            }).ToList();

            return result;
        }

        public static Tuple<decimal, decimal, decimal> PackageCompute(decimal totalAmount, PackageProductModel package)
        {
            decimal vight = package.VightFood.Sum(w => w.TotalPrice);
            if (vight > totalAmount)
                vight = totalAmount;

            decimal bar = package.BarFood.Sum(w => w.TotalPrice);
            if ((totalAmount - vight) < bar)
                bar = (totalAmount - vight);

            decimal infield = totalAmount - vight - bar;
            if (infield < 0)
                infield = 0;

            return new Tuple<decimal, decimal, decimal>(vight, bar, infield);
        }
    }
}
