﻿using ComponentApplicationServiceInterface.Context.Response;
using Pos.Model.DbFood.Context;
using Pos.Model.DbFood.RespModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Pos.Service.InterfaceService
{
    [ServiceContract]
    public interface IFoodService
    {
        [OperationContract]
        ResponseContext<List<GetStoreReportModel>> GetStoreReport(GetStoreReportContext context);

        [OperationContract]
        ResponseContext<List<GetStoreFtTypeReportModel>> GetTypeDetailReport(GetStoreFtTypeReportContext context);

        [OperationContract]
        ResponseContext<List<GetHeadCountModel>> GetHeadCountReport(GetStoreReportContext context);

        [OperationContract]
        ResponseContext<List<WaitCreateModel>> GetWaitCreate(WaitCreateContext context);

        [OperationContract]
        ResponseContext<List<GetFoodInfoModel>> GetFoodInfo(GetFoodInfoContext context);
    }
}
