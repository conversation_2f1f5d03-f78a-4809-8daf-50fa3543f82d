﻿using ComponentCore.Windows.Services;
using ComponentCore.Windows.Services.Wcf;
using Pos.Drive;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Pos.Service
{
    class Program
    {

        [STAThread]
        static void Main(string[] args)
        {
            WcfServiceConsole service = new WcfServiceConsole("HD.Pos.Service", "HD.Pos.Service", args, OnStart, null);
            service.Start();
            Console.ReadLine();
        }

        public static void OnStart()
        {
            RegisterWindowsService service = new RegisterWindowsService();

            service.open(typeof(PosService));
        }

    }
}
