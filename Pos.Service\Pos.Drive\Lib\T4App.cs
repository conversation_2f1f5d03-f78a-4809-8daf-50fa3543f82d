﻿
using Pos.Application;
using Pos.Model.DbContext;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
namespace Pos.Drive
{
 public partial class AppSession {

  AddItemApp _AddItemApp;
    public AddItemApp AddItem
        {
            get
            {
                if (_AddItemApp == null) _AddItemApp = new AddItemApp();
                return _AddItemApp;
            }
        }
        
 

  


  AiTypeApp _AiTypeApp;
    public AiTypeApp AiType
        {
            get
            {
                if (_AiTypeApp == null) _AiTypeApp = new AiTypeApp();
                return _AiTypeApp;
            }
        }
        
 

  


  AmountLogApp _AmountLogApp;
    public AmountLogApp AmountLog
        {
            get
            {
                if (_AmountLogApp == null) _AmountLogApp = new AmountLogApp();
                return _AmountLogApp;
            }
        }
        
 

  


  CarLeaveLogApp _CarLeaveLogApp;
    public CarLeaveLogApp CarLeaveLog
        {
            get
            {
                if (_CarLeaveLogApp == null) _CarLeaveLogApp = new CarLeaveLogApp();
                return _CarLeaveLogApp;
            }
        }
        
 

  


  CashierApp _CashierApp;
    public CashierApp Cashier
        {
            get
            {
                if (_CashierApp == null) _CashierApp = new CashierApp();
                return _CashierApp;
            }
        }
        
 

  


  ClearDataLogApp _ClearDataLogApp;
    public ClearDataLogApp ClearDataLog
        {
            get
            {
                if (_ClearDataLogApp == null) _ClearDataLogApp = new ClearDataLogApp();
                return _ClearDataLogApp;
            }
        }
        
 

  


  DeadLockLogApp _DeadLockLogApp;
    public DeadLockLogApp DeadLockLog
        {
            get
            {
                if (_DeadLockLogApp == null) _DeadLockLogApp = new DeadLockLogApp();
                return _DeadLockLogApp;
            }
        }
        
 

  


  DepositInfoApp _DepositInfoApp;
    public DepositInfoApp DepositInfo
        {
            get
            {
                if (_DepositInfoApp == null) _DepositInfoApp = new DepositInfoApp();
                return _DepositInfoApp;
            }
        }
        
 

  


  DeptApp _DeptApp;
    public DeptApp Dept
        {
            get
            {
                if (_DeptApp == null) _DeptApp = new DeptApp();
                return _DeptApp;
            }
        }
        
 

  


  DeptBanSetApp _DeptBanSetApp;
    public DeptBanSetApp DeptBanSet
        {
            get
            {
                if (_DeptBanSetApp == null) _DeptBanSetApp = new DeptBanSetApp();
                return _DeptBanSetApp;
            }
        }
        
 

  


  dtpropertiesApp _dtpropertiesApp;
    public dtpropertiesApp dtproperties
        {
            get
            {
                if (_dtpropertiesApp == null) _dtpropertiesApp = new dtpropertiesApp();
                return _dtpropertiesApp;
            }
        }
        
 

  


  FdCashApp _FdCashApp;
    public FdCashApp FdCash
        {
            get
            {
                if (_FdCashApp == null) _FdCashApp = new FdCashApp();
                return _FdCashApp;
            }
        }
        
 

  


  FdCash_TrackApp _FdCash_TrackApp;
    public FdCash_TrackApp FdCash_Track
        {
            get
            {
                if (_FdCash_TrackApp == null) _FdCash_TrackApp = new FdCash_TrackApp();
                return _FdCash_TrackApp;
            }
        }
        
 

  


  FdCashBakApp _FdCashBakApp;
    public FdCashBakApp FdCashBak
        {
            get
            {
                if (_FdCashBakApp == null) _FdCashBakApp = new FdCashBakApp();
                return _FdCashBakApp;
            }
        }
        
 

  


  FdCashBak_BApp _FdCashBak_BApp;
    public FdCashBak_BApp FdCashBak_B
        {
            get
            {
                if (_FdCashBak_BApp == null) _FdCashBak_BApp = new FdCashBak_BApp();
                return _FdCashBak_BApp;
            }
        }
        
 

  


  FdCashBak_BakApp _FdCashBak_BakApp;
    public FdCashBak_BakApp FdCashBak_Bak
        {
            get
            {
                if (_FdCashBak_BakApp == null) _FdCashBak_BakApp = new FdCashBak_BakApp();
                return _FdCashBak_BakApp;
            }
        }
        
 

  


  FdDetTypeApp _FdDetTypeApp;
    public FdDetTypeApp FdDetType
        {
            get
            {
                if (_FdDetTypeApp == null) _FdDetTypeApp = new FdDetTypeApp();
                return _FdDetTypeApp;
            }
        }
        
 

  


  FdImageApp _FdImageApp;
    public FdImageApp FdImage
        {
            get
            {
                if (_FdImageApp == null) _FdImageApp = new FdImageApp();
                return _FdImageApp;
            }
        }
        
 

  


  FdInvApp _FdInvApp;
    public FdInvApp FdInv
        {
            get
            {
                if (_FdInvApp == null) _FdInvApp = new FdInvApp();
                return _FdInvApp;
            }
        }
        
 

  


  FdInv_BApp _FdInv_BApp;
    public FdInv_BApp FdInv_B
        {
            get
            {
                if (_FdInv_BApp == null) _FdInv_BApp = new FdInv_BApp();
                return _FdInv_BApp;
            }
        }
        
 

  


  FdInv_BakApp _FdInv_BakApp;
    public FdInv_BakApp FdInv_Bak
        {
            get
            {
                if (_FdInv_BakApp == null) _FdInv_BakApp = new FdInv_BakApp();
                return _FdInv_BakApp;
            }
        }
        
 

  


  FdInv_ExchangeLogApp _FdInv_ExchangeLogApp;
    public FdInv_ExchangeLogApp FdInv_ExchangeLog
        {
            get
            {
                if (_FdInv_ExchangeLogApp == null) _FdInv_ExchangeLogApp = new FdInv_ExchangeLogApp();
                return _FdInv_ExchangeLogApp;
            }
        }
        
 

  


  FdInvCashItemApp _FdInvCashItemApp;
    public FdInvCashItemApp FdInvCashItem
        {
            get
            {
                if (_FdInvCashItemApp == null) _FdInvCashItemApp = new FdInvCashItemApp();
                return _FdInvCashItemApp;
            }
        }
        
 

  


  FdInvDescApp _FdInvDescApp;
    public FdInvDescApp FdInvDesc
        {
            get
            {
                if (_FdInvDescApp == null) _FdInvDescApp = new FdInvDescApp();
                return _FdInvDescApp;
            }
        }
        
 

  


  FdTicketApp _FdTicketApp;
    public FdTicketApp FdTicket
        {
            get
            {
                if (_FdTicketApp == null) _FdTicketApp = new FdTicketApp();
                return _FdTicketApp;
            }
        }
        
 

  


  FdTimePriceApp _FdTimePriceApp;
    public FdTimePriceApp FdTimePrice
        {
            get
            {
                if (_FdTimePriceApp == null) _FdTimePriceApp = new FdTimePriceApp();
                return _FdTimePriceApp;
            }
        }
        
 

  


  FdTimeZoneApp _FdTimeZoneApp;
    public FdTimeZoneApp FdTimeZone
        {
            get
            {
                if (_FdTimeZoneApp == null) _FdTimeZoneApp = new FdTimeZoneApp();
                return _FdTimeZoneApp;
            }
        }
        
 

  


  FdTypeApp _FdTypeApp;
    public FdTypeApp FdType
        {
            get
            {
                if (_FdTypeApp == null) _FdTypeApp = new FdTypeApp();
                return _FdTypeApp;
            }
        }
        
 

  


  FdUserApp _FdUserApp;
    public FdUserApp FdUser
        {
            get
            {
                if (_FdUserApp == null) _FdUserApp = new FdUserApp();
                return _FdUserApp;
            }
        }
        
 

  


  FdUserGradeApp _FdUserGradeApp;
    public FdUserGradeApp FdUserGrade
        {
            get
            {
                if (_FdUserGradeApp == null) _FdUserGradeApp = new FdUserGradeApp();
                return _FdUserGradeApp;
            }
        }
        
 

  


  FdUserRightsApp _FdUserRightsApp;
    public FdUserRightsApp FdUserRights
        {
            get
            {
                if (_FdUserRightsApp == null) _FdUserRightsApp = new FdUserRightsApp();
                return _FdUserRightsApp;
            }
        }
        
 

  


  FestivalTimeApp _FestivalTimeApp;
    public FestivalTimeApp FestivalTime
        {
            get
            {
                if (_FestivalTimeApp == null) _FestivalTimeApp = new FestivalTimeApp();
                return _FestivalTimeApp;
            }
        }
        
 

  


  FoodApp _FoodApp;
    public FoodApp Food
        {
            get
            {
                if (_FoodApp == null) _FoodApp = new FoodApp();
                return _FoodApp;
            }
        }
        
 

  


  FoodCalApp _FoodCalApp;
    public FoodCalApp FoodCal
        {
            get
            {
                if (_FoodCalApp == null) _FoodCalApp = new FoodCalApp();
                return _FoodCalApp;
            }
        }
        
 

  


  FoodLabelApp _FoodLabelApp;
    public FoodLabelApp FoodLabel
        {
            get
            {
                if (_FoodLabelApp == null) _FoodLabelApp = new FoodLabelApp();
                return _FoodLabelApp;
            }
        }
        
 

  


  FoodOrderMApp _FoodOrderMApp;
    public FoodOrderMApp FoodOrderM
        {
            get
            {
                if (_FoodOrderMApp == null) _FoodOrderMApp = new FoodOrderMApp();
                return _FoodOrderMApp;
            }
        }
        
 

  


  FPrnApp _FPrnApp;
    public FPrnApp FPrn
        {
            get
            {
                if (_FPrnApp == null) _FPrnApp = new FPrnApp();
                return _FPrnApp;
            }
        }
        
 

  


  FPrnDataApp _FPrnDataApp;
    public FPrnDataApp FPrnData
        {
            get
            {
                if (_FPrnDataApp == null) _FPrnDataApp = new FPrnDataApp();
                return _FPrnDataApp;
            }
        }
        
 

  


  FPrnData_BakApp _FPrnData_BakApp;
    public FPrnData_BakApp FPrnData_Bak
        {
            get
            {
                if (_FPrnData_BakApp == null) _FPrnData_BakApp = new FPrnData_BakApp();
                return _FPrnData_BakApp;
            }
        }
        
 

  


  FreePackageCoupon_RecordApp _FreePackageCoupon_RecordApp;
    public FreePackageCoupon_RecordApp FreePackageCoupon_Record
        {
            get
            {
                if (_FreePackageCoupon_RecordApp == null) _FreePackageCoupon_RecordApp = new FreePackageCoupon_RecordApp();
                return _FreePackageCoupon_RecordApp;
            }
        }
        
 

  


  FtInfoApp _FtInfoApp;
    public FtInfoApp FtInfo
        {
            get
            {
                if (_FtInfoApp == null) _FtInfoApp = new FtInfoApp();
                return _FtInfoApp;
            }
        }
        
 

  


  GDDB20InfoApp _GDDB20InfoApp;
    public GDDB20InfoApp GDDB20Info
        {
            get
            {
                if (_GDDB20InfoApp == null) _GDDB20InfoApp = new GDDB20InfoApp();
                return _GDDB20InfoApp;
            }
        }
        
 

  


  GDDBInfoApp _GDDBInfoApp;
    public GDDBInfoApp GDDBInfo
        {
            get
            {
                if (_GDDBInfoApp == null) _GDDBInfoApp = new GDDBInfoApp();
                return _GDDBInfoApp;
            }
        }
        
 

  


  HappyRabApp _HappyRabApp;
    public HappyRabApp HappyRab
        {
            get
            {
                if (_HappyRabApp == null) _HappyRabApp = new HappyRabApp();
                return _HappyRabApp;
            }
        }
        
 

  


  HolidayApp _HolidayApp;
    public HolidayApp Holiday
        {
            get
            {
                if (_HolidayApp == null) _HolidayApp = new HolidayApp();
                return _HolidayApp;
            }
        }
        
 

  


  HotFdTypeApp _HotFdTypeApp;
    public HotFdTypeApp HotFdType
        {
            get
            {
                if (_HotFdTypeApp == null) _HotFdTypeApp = new HotFdTypeApp();
                return _HotFdTypeApp;
            }
        }
        
 

  


  HotFoodApp _HotFoodApp;
    public HotFoodApp HotFood
        {
            get
            {
                if (_HotFoodApp == null) _HotFoodApp = new HotFoodApp();
                return _HotFoodApp;
            }
        }
        
 

  


  Inv_TimeSectionApp _Inv_TimeSectionApp;
    public Inv_TimeSectionApp Inv_TimeSection
        {
            get
            {
                if (_Inv_TimeSectionApp == null) _Inv_TimeSectionApp = new Inv_TimeSectionApp();
                return _Inv_TimeSectionApp;
            }
        }
        
 

  


  InvRollBackApp _InvRollBackApp;
    public InvRollBackApp InvRollBack
        {
            get
            {
                if (_InvRollBackApp == null) _InvRollBackApp = new InvRollBackApp();
                return _InvRollBackApp;
            }
        }
        
 

  


  LanIdApp _LanIdApp;
    public LanIdApp LanId
        {
            get
            {
                if (_LanIdApp == null) _LanIdApp = new LanIdApp();
                return _LanIdApp;
            }
        }
        
 

  


  LanStringApp _LanStringApp;
    public LanStringApp LanString
        {
            get
            {
                if (_LanStringApp == null) _LanStringApp = new LanStringApp();
                return _LanStringApp;
            }
        }
        
 

  


  LastInvNoApp _LastInvNoApp;
    public LastInvNoApp LastInvNo
        {
            get
            {
                if (_LastInvNoApp == null) _LastInvNoApp = new LastInvNoApp();
                return _LastInvNoApp;
            }
        }
        
 

  


  LastRefNoApp _LastRefNoApp;
    public LastRefNoApp LastRefNo
        {
            get
            {
                if (_LastRefNoApp == null) _LastRefNoApp = new LastRefNoApp();
                return _LastRefNoApp;
            }
        }
        
 

  


  meal_distribution_infoApp _meal_distribution_infoApp;
    public meal_distribution_infoApp meal_distribution_info
        {
            get
            {
                if (_meal_distribution_infoApp == null) _meal_distribution_infoApp = new meal_distribution_infoApp();
                return _meal_distribution_infoApp;
            }
        }
        
 

  


  meal_infoApp _meal_infoApp;
    public meal_infoApp meal_info
        {
            get
            {
                if (_meal_infoApp == null) _meal_infoApp = new meal_infoApp();
                return _meal_infoApp;
            }
        }
        
 

  


  MembAmountEditLogApp _MembAmountEditLogApp;
    public MembAmountEditLogApp MembAmountEditLog
        {
            get
            {
                if (_MembAmountEditLogApp == null) _MembAmountEditLogApp = new MembAmountEditLogApp();
                return _MembAmountEditLogApp;
            }
        }
        
 

  


  MemberApp _MemberApp;
    public MemberApp Member
        {
            get
            {
                if (_MemberApp == null) _MemberApp = new MemberApp();
                return _MemberApp;
            }
        }
        
 

  


  MemberCheckoutInfoApp _MemberCheckoutInfoApp;
    public MemberCheckoutInfoApp MemberCheckoutInfo
        {
            get
            {
                if (_MemberCheckoutInfoApp == null) _MemberCheckoutInfoApp = new MemberCheckoutInfoApp();
                return _MemberCheckoutInfoApp;
            }
        }
        
 

  


  MemberGiveSetApp _MemberGiveSetApp;
    public MemberGiveSetApp MemberGiveSet
        {
            get
            {
                if (_MemberGiveSetApp == null) _MemberGiveSetApp = new MemberGiveSetApp();
                return _MemberGiveSetApp;
            }
        }
        
 

  


  MembSetApp _MembSetApp;
    public MembSetApp MembSet
        {
            get
            {
                if (_MembSetApp == null) _MembSetApp = new MembSetApp();
                return _MembSetApp;
            }
        }
        
 

  


  MGradeFdDiscApp _MGradeFdDiscApp;
    public MGradeFdDiscApp MGradeFdDisc
        {
            get
            {
                if (_MGradeFdDiscApp == null) _MGradeFdDiscApp = new MGradeFdDiscApp();
                return _MGradeFdDiscApp;
            }
        }
        
 

  


  MobileFdGiveApp _MobileFdGiveApp;
    public MobileFdGiveApp MobileFdGive
        {
            get
            {
                if (_MobileFdGiveApp == null) _MobileFdGiveApp = new MobileFdGiveApp();
                return _MobileFdGiveApp;
            }
        }
        
 

  


  MobileFoodApp _MobileFoodApp;
    public MobileFoodApp MobileFood
        {
            get
            {
                if (_MobileFoodApp == null) _MobileFoodApp = new MobileFoodApp();
                return _MobileFoodApp;
            }
        }
        
 

  


  MobileFoodDiscApp _MobileFoodDiscApp;
    public MobileFoodDiscApp MobileFoodDisc
        {
            get
            {
                if (_MobileFoodDiscApp == null) _MobileFoodDiscApp = new MobileFoodDiscApp();
                return _MobileFoodDiscApp;
            }
        }
        
 

  


  MobileFtTypeApp _MobileFtTypeApp;
    public MobileFtTypeApp MobileFtType
        {
            get
            {
                if (_MobileFtTypeApp == null) _MobileFtTypeApp = new MobileFtTypeApp();
                return _MobileFtTypeApp;
            }
        }
        
 

  


  MobilePackGiveApp _MobilePackGiveApp;
    public MobilePackGiveApp MobilePackGive
        {
            get
            {
                if (_MobilePackGiveApp == null) _MobilePackGiveApp = new MobilePackGiveApp();
                return _MobilePackGiveApp;
            }
        }
        
 

  


  MobilOrderItemApp _MobilOrderItemApp;
    public MobilOrderItemApp MobilOrderItem
        {
            get
            {
                if (_MobilOrderItemApp == null) _MobilOrderItemApp = new MobilOrderItemApp();
                return _MobilOrderItemApp;
            }
        }
        
 

  


  MobilOrderTitleApp _MobilOrderTitleApp;
    public MobilOrderTitleApp MobilOrderTitle
        {
            get
            {
                if (_MobilOrderTitleApp == null) _MobilOrderTitleApp = new MobilOrderTitleApp();
                return _MobilOrderTitleApp;
            }
        }
        
 

  


  MobilUserOrderTitleApp _MobilUserOrderTitleApp;
    public MobilUserOrderTitleApp MobilUserOrderTitle
        {
            get
            {
                if (_MobilUserOrderTitleApp == null) _MobilUserOrderTitleApp = new MobilUserOrderTitleApp();
                return _MobilUserOrderTitleApp;
            }
        }
        
 

  


  NewMemberLogApp _NewMemberLogApp;
    public NewMemberLogApp NewMemberLog
        {
            get
            {
                if (_NewMemberLogApp == null) _NewMemberLogApp = new NewMemberLogApp();
                return _NewMemberLogApp;
            }
        }
        
 

  


  ParamSetApp _ParamSetApp;
    public ParamSetApp ParamSet
        {
            get
            {
                if (_ParamSetApp == null) _ParamSetApp = new ParamSetApp();
                return _ParamSetApp;
            }
        }
        
 

  


  pre_orderApp _pre_orderApp;
    public pre_orderApp pre_order
        {
            get
            {
                if (_pre_orderApp == null) _pre_orderApp = new pre_orderApp();
                return _pre_orderApp;
            }
        }
        
 

  


  PreOrderSendMsgInfoApp _PreOrderSendMsgInfoApp;
    public PreOrderSendMsgInfoApp PreOrderSendMsgInfo
        {
            get
            {
                if (_PreOrderSendMsgInfoApp == null) _PreOrderSendMsgInfoApp = new PreOrderSendMsgInfoApp();
                return _PreOrderSendMsgInfoApp;
            }
        }
        
 

  


  PriceNoApp _PriceNoApp;
    public PriceNoApp PriceNo
        {
            get
            {
                if (_PriceNoApp == null) _PriceNoApp = new PriceNoApp();
                return _PriceNoApp;
            }
        }
        
 

  


  QrInfoApp _QrInfoApp;
    public QrInfoApp QrInfo
        {
            get
            {
                if (_QrInfoApp == null) _QrInfoApp = new QrInfoApp();
                return _QrInfoApp;
            }
        }
        
 

  


  RecordRoomTimeApp _RecordRoomTimeApp;
    public RecordRoomTimeApp RecordRoomTime
        {
            get
            {
                if (_RecordRoomTimeApp == null) _RecordRoomTimeApp = new RecordRoomTimeApp();
                return _RecordRoomTimeApp;
            }
        }
        
 

  


  RefToZDLogApp _RefToZDLogApp;
    public RefToZDLogApp RefToZDLog
        {
            get
            {
                if (_RefToZDLogApp == null) _RefToZDLogApp = new RefToZDLogApp();
                return _RefToZDLogApp;
            }
        }
        
 

  


  RightSetApp _RightSetApp;
    public RightSetApp RightSet
        {
            get
            {
                if (_RightSetApp == null) _RightSetApp = new RightSetApp();
                return _RightSetApp;
            }
        }
        
 

  


  RmAccountInfoApp _RmAccountInfoApp;
    public RmAccountInfoApp RmAccountInfo
        {
            get
            {
                if (_RmAccountInfoApp == null) _RmAccountInfoApp = new RmAccountInfoApp();
                return _RmAccountInfoApp;
            }
        }
        
 

  


  RmAreaApp _RmAreaApp;
    public RmAreaApp RmArea
        {
            get
            {
                if (_RmAreaApp == null) _RmAreaApp = new RmAreaApp();
                return _RmAreaApp;
            }
        }
        
 

  


  RmClearLogApp _RmClearLogApp;
    public RmClearLogApp RmClearLog
        {
            get
            {
                if (_RmClearLogApp == null) _RmClearLogApp = new RmClearLogApp();
                return _RmClearLogApp;
            }
        }
        
 

  


  RmCloseInfoApp _RmCloseInfoApp;
    public RmCloseInfoApp RmCloseInfo
        {
            get
            {
                if (_RmCloseInfoApp == null) _RmCloseInfoApp = new RmCloseInfoApp();
                return _RmCloseInfoApp;
            }
        }
        
 

  


  RmCloseInfo_CollectApp _RmCloseInfo_CollectApp;
    public RmCloseInfo_CollectApp RmCloseInfo_Collect
        {
            get
            {
                if (_RmCloseInfo_CollectApp == null) _RmCloseInfo_CollectApp = new RmCloseInfo_CollectApp();
                return _RmCloseInfo_CollectApp;
            }
        }
        
 

  


  RmExchangeDetailApp _RmExchangeDetailApp;
    public RmExchangeDetailApp RmExchangeDetail
        {
            get
            {
                if (_RmExchangeDetailApp == null) _RmExchangeDetailApp = new RmExchangeDetailApp();
                return _RmExchangeDetailApp;
            }
        }
        
 

  


  RmExchangeLogApp _RmExchangeLogApp;
    public RmExchangeLogApp RmExchangeLog
        {
            get
            {
                if (_RmExchangeLogApp == null) _RmExchangeLogApp = new RmExchangeLogApp();
                return _RmExchangeLogApp;
            }
        }
        
 

  


  RmFtPrnIndexApp _RmFtPrnIndexApp;
    public RmFtPrnIndexApp RmFtPrnIndex
        {
            get
            {
                if (_RmFtPrnIndexApp == null) _RmFtPrnIndexApp = new RmFtPrnIndexApp();
                return _RmFtPrnIndexApp;
            }
        }
        
 

  


  RmOrderApp _RmOrderApp;
    public RmOrderApp RmOrder
        {
            get
            {
                if (_RmOrderApp == null) _RmOrderApp = new RmOrderApp();
                return _RmOrderApp;
            }
        }
        
 

  


  RmOrderDelLogApp _RmOrderDelLogApp;
    public RmOrderDelLogApp RmOrderDelLog
        {
            get
            {
                if (_RmOrderDelLogApp == null) _RmOrderDelLogApp = new RmOrderDelLogApp();
                return _RmOrderDelLogApp;
            }
        }
        
 

  


  RmOrderLogApp _RmOrderLogApp;
    public RmOrderLogApp RmOrderLog
        {
            get
            {
                if (_RmOrderLogApp == null) _RmOrderLogApp = new RmOrderLogApp();
                return _RmOrderLogApp;
            }
        }
        
 

  


  RmsRoomApp _RmsRoomApp;
    public RmsRoomApp RmsRoom
        {
            get
            {
                if (_RmsRoomApp == null) _RmsRoomApp = new RmsRoomApp();
                return _RmsRoomApp;
            }
        }
        
 

  


  RmTypeApp _RmTypeApp;
    public RmTypeApp RmType
        {
            get
            {
                if (_RmTypeApp == null) _RmTypeApp = new RmTypeApp();
                return _RmTypeApp;
            }
        }
        
 

  


  RoomApp _RoomApp;
    public RoomApp Room
        {
            get
            {
                if (_RoomApp == null) _RoomApp = new RoomApp();
                return _RoomApp;
            }
        }
        
 

  


  RoomCommissionApp _RoomCommissionApp;
    public RoomCommissionApp RoomCommission
        {
            get
            {
                if (_RoomCommissionApp == null) _RoomCommissionApp = new RoomCommissionApp();
                return _RoomCommissionApp;
            }
        }
        
 

  


  RoomExtendApp _RoomExtendApp;
    public RoomExtendApp RoomExtend
        {
            get
            {
                if (_RoomExtendApp == null) _RoomExtendApp = new RoomExtendApp();
                return _RoomExtendApp;
            }
        }
        
 

  


  RoomtestApp _RoomtestApp;
    public RoomtestApp Roomtest
        {
            get
            {
                if (_RoomtestApp == null) _RoomtestApp = new RoomtestApp();
                return _RoomtestApp;
            }
        }
        
 

  


  RtAutoApp _RtAutoApp;
    public RtAutoApp RtAuto
        {
            get
            {
                if (_RtAutoApp == null) _RtAutoApp = new RtAutoApp();
                return _RtAutoApp;
            }
        }
        
 

  


  RtAutoZDApp _RtAutoZDApp;
    public RtAutoZDApp RtAutoZD
        {
            get
            {
                if (_RtAutoZDApp == null) _RtAutoZDApp = new RtAutoZDApp();
                return _RtAutoZDApp;
            }
        }
        
 

  


  RtTimePriceApp _RtTimePriceApp;
    public RtTimePriceApp RtTimePrice
        {
            get
            {
                if (_RtTimePriceApp == null) _RtTimePriceApp = new RtTimePriceApp();
                return _RtTimePriceApp;
            }
        }
        
 

  


  S_AccTypeApp _S_AccTypeApp;
    public S_AccTypeApp S_AccType
        {
            get
            {
                if (_S_AccTypeApp == null) _S_AccTypeApp = new S_AccTypeApp();
                return _S_AccTypeApp;
            }
        }
        
 

  


  S_CashItemApp _S_CashItemApp;
    public S_CashItemApp S_CashItem
        {
            get
            {
                if (_S_CashItemApp == null) _S_CashItemApp = new S_CashItemApp();
                return _S_CashItemApp;
            }
        }
        
 

  


  S_PrnTypeApp _S_PrnTypeApp;
    public S_PrnTypeApp S_PrnType
        {
            get
            {
                if (_S_PrnTypeApp == null) _S_PrnTypeApp = new S_PrnTypeApp();
                return _S_PrnTypeApp;
            }
        }
        
 

  


  S_RmStatusApp _S_RmStatusApp;
    public S_RmStatusApp S_RmStatus
        {
            get
            {
                if (_S_RmStatusApp == null) _S_RmStatusApp = new S_RmStatusApp();
                return _S_RmStatusApp;
            }
        }
        
 

  


  SchedulingRecordApp _SchedulingRecordApp;
    public SchedulingRecordApp SchedulingRecord
        {
            get
            {
                if (_SchedulingRecordApp == null) _SchedulingRecordApp = new SchedulingRecordApp();
                return _SchedulingRecordApp;
            }
        }
        
 

  


  SDateApp _SDateApp;
    public SDateApp SDate
        {
            get
            {
                if (_SDateApp == null) _SDateApp = new SDateApp();
                return _SDateApp;
            }
        }
        
 

  


  ShareSetInfoApp _ShareSetInfoApp;
    public ShareSetInfoApp ShareSetInfo
        {
            get
            {
                if (_ShareSetInfoApp == null) _ShareSetInfoApp = new ShareSetInfoApp();
                return _ShareSetInfoApp;
            }
        }
        
 

  


  ShiftInfoApp _ShiftInfoApp;
    public ShiftInfoApp ShiftInfo
        {
            get
            {
                if (_ShiftInfoApp == null) _ShiftInfoApp = new ShiftInfoApp();
                return _ShiftInfoApp;
            }
        }
        
 

  


  StarInfoApp _StarInfoApp;
    public StarInfoApp StarInfo
        {
            get
            {
                if (_StarInfoApp == null) _StarInfoApp = new StarInfoApp();
                return _StarInfoApp;
            }
        }
        
 

  


  TestTableApp _TestTableApp;
    public TestTableApp TestTable
        {
            get
            {
                if (_TestTableApp == null) _TestTableApp = new TestTableApp();
                return _TestTableApp;
            }
        }
        
 

  


  Th_RoomCommissionAllotApp _Th_RoomCommissionAllotApp;
    public Th_RoomCommissionAllotApp Th_RoomCommissionAllot
        {
            get
            {
                if (_Th_RoomCommissionAllotApp == null) _Th_RoomCommissionAllotApp = new Th_RoomCommissionAllotApp();
                return _Th_RoomCommissionAllotApp;
            }
        }
        
 

  


  TimeZoneApp _TimeZoneApp;
    public TimeZoneApp TimeZone
        {
            get
            {
                if (_TimeZoneApp == null) _TimeZoneApp = new TimeZoneApp();
                return _TimeZoneApp;
            }
        }
        
 

  


  triggerRecordApp _triggerRecordApp;
    public triggerRecordApp triggerRecord
        {
            get
            {
                if (_triggerRecordApp == null) _triggerRecordApp = new triggerRecordApp();
                return _triggerRecordApp;
            }
        }
        
 

  


  UserAmountApp _UserAmountApp;
    public UserAmountApp UserAmount
        {
            get
            {
                if (_UserAmountApp == null) _UserAmountApp = new UserAmountApp();
                return _UserAmountApp;
            }
        }
        
 

  


  UserAmountDetailApp _UserAmountDetailApp;
    public UserAmountDetailApp UserAmountDetail
        {
            get
            {
                if (_UserAmountDetailApp == null) _UserAmountDetailApp = new UserAmountDetailApp();
                return _UserAmountDetailApp;
            }
        }
        
 

  


  UserFtZDApp _UserFtZDApp;
    public UserFtZDApp UserFtZD
        {
            get
            {
                if (_UserFtZDApp == null) _UserFtZDApp = new UserFtZDApp();
                return _UserFtZDApp;
            }
        }
        
 

  


  UserIOApp _UserIOApp;
    public UserIOApp UserIO
        {
            get
            {
                if (_UserIOApp == null) _UserIOApp = new UserIOApp();
                return _UserIOApp;
            }
        }
        
 

  


  UserZDItemApp _UserZDItemApp;
    public UserZDItemApp UserZDItem
        {
            get
            {
                if (_UserZDItemApp == null) _UserZDItemApp = new UserZDItemApp();
                return _UserZDItemApp;
            }
        }
        
 

  


  UserZDItemDetailApp _UserZDItemDetailApp;
    public UserZDItemDetailApp UserZDItemDetail
        {
            get
            {
                if (_UserZDItemDetailApp == null) _UserZDItemDetailApp = new UserZDItemDetailApp();
                return _UserZDItemDetailApp;
            }
        }
        
 

  


  UserZDSetApp _UserZDSetApp;
    public UserZDSetApp UserZDSet
        {
            get
            {
                if (_UserZDSetApp == null) _UserZDSetApp = new UserZDSetApp();
                return _UserZDSetApp;
            }
        }
        
 

  


  VesaApp _VesaApp;
    public VesaApp Vesa
        {
            get
            {
                if (_VesaApp == null) _VesaApp = new VesaApp();
                return _VesaApp;
            }
        }
        
 

  


  WebOrderTableApp _WebOrderTableApp;
    public WebOrderTableApp WebOrderTable
        {
            get
            {
                if (_WebOrderTableApp == null) _WebOrderTableApp = new WebOrderTableApp();
                return _WebOrderTableApp;
            }
        }
        
 

  


  WeChatFoodOrderMsgApp _WeChatFoodOrderMsgApp;
    public WeChatFoodOrderMsgApp WeChatFoodOrderMsg
        {
            get
            {
                if (_WeChatFoodOrderMsgApp == null) _WeChatFoodOrderMsgApp = new WeChatFoodOrderMsgApp();
                return _WeChatFoodOrderMsgApp;
            }
        }
        
 

  


  WeChatFoodOrderMsg2App _WeChatFoodOrderMsg2App;
    public WeChatFoodOrderMsg2App WeChatFoodOrderMsg2
        {
            get
            {
                if (_WeChatFoodOrderMsg2App == null) _WeChatFoodOrderMsg2App = new WeChatFoodOrderMsg2App();
                return _WeChatFoodOrderMsg2App;
            }
        }
        
 

  


  WindTicketApp _WindTicketApp;
    public WindTicketApp WindTicket
        {
            get
            {
                if (_WindTicketApp == null) _WindTicketApp = new WindTicketApp();
                return _WindTicketApp;
            }
        }
        
 

  


  wx_shopmall_worktimeApp _wx_shopmall_worktimeApp;
    public wx_shopmall_worktimeApp wx_shopmall_worktime
        {
            get
            {
                if (_wx_shopmall_worktimeApp == null) _wx_shopmall_worktimeApp = new wx_shopmall_worktimeApp();
                return _wx_shopmall_worktimeApp;
            }
        }
        
 

  


  wxPayCheckInfoApp _wxPayCheckInfoApp;
    public wxPayCheckInfoApp wxPayCheckInfo
        {
            get
            {
                if (_wxPayCheckInfoApp == null) _wxPayCheckInfoApp = new wxPayCheckInfoApp();
                return _wxPayCheckInfoApp;
            }
        }
        
 

  


  wxPayInfoApp _wxPayInfoApp;
    public wxPayInfoApp wxPayInfo
        {
            get
            {
                if (_wxPayInfoApp == null) _wxPayInfoApp = new wxPayInfoApp();
                return _wxPayInfoApp;
            }
        }
        
 

  

 }
}
