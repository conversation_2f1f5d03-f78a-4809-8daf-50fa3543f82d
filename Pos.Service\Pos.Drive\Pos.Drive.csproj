﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{E3E2EF69-7DC1-461B-89EF-113E78DC84BD}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Pos.Drive</RootNamespace>
    <AssemblyName>Pos.Drive</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ComponentApplicationServiceInterface">
      <HintPath>..\Pos.Model\Package\ComponentApplicationServiceInterface.dll</HintPath>
    </Reference>
    <Reference Include="ComponentCore">
      <HintPath>..\Pos.Model\Package\ComponentCore.dll</HintPath>
    </Reference>
    <Reference Include="SERVICE.PROXY">
      <HintPath>..\Pos.Model\Package\SERVICE.PROXY.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DbFood\FoodLabelDrive.cs" />
    <Compile Include="DbFood\OrderServiceDrive.cs" />
    <Compile Include="DbFood\ProductCalculation\Impl\Count_Default.cs" />
    <Compile Include="DbFood\ProductCalculation\Impl\Count_Type.cs" />
    <Compile Include="DbFood\ProductCalculation\ProductCalculationBase.cs" />
    <Compile Include="DbFood\ProductCalculation\UnifiedComputingSystem.cs" />
    <Compile Include="DbFood\RoomServiceDrive.cs" />
    <Compile Include="DriveBase.cs" />
    <Compile Include="DbFood\FoodDrive.cs" />
    <Compile Include="Lib\T4App.cs">
      <DependentUpon>T4App.tt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Pos.Application\Pos.Application.csproj">
      <Project>{1ec5741d-6cd4-401f-a868-e4d6df406142}</Project>
      <Name>Pos.Application</Name>
    </ProjectReference>
    <ProjectReference Include="..\Pos.Common\Pos.Common.csproj">
      <Project>{b7127faf-ed89-4210-8adc-ec29099ce7fd}</Project>
      <Name>Pos.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Pos.Domain\Pos.Domain.csproj">
      <Project>{48b91ac4-ed2f-48c4-84ff-263cfe5ab65a}</Project>
      <Name>Pos.Domain</Name>
    </ProjectReference>
    <ProjectReference Include="..\Pos.Model\Pos.Model.csproj">
      <Project>{0752c409-20ca-41b4-b9b3-bebc6d1602bd}</Project>
      <Name>Pos.Model</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="Lib\T4App.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>T4App.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>