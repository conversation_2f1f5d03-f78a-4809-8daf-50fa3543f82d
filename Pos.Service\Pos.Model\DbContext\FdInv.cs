//------------------------------------------------------------------------------
// <auto-generated>
//    此代码是根据模板生成的。
//
//    手动更改此文件可能会导致应用程序中发生异常行为。
//    如果重新生成代码，则将覆盖对此文件的手动更改。
// </auto-generated>
//------------------------------------------------------------------------------

namespace Pos.Model.DbContext
{
    using System;
    using System.Collections.Generic;
    
    public partial class FdInv
    {
        public FdInv()
        {
            this.FdCashBak = new HashSet<FdCashBak>();
            this.FdInv_ExchangeLog = new HashSet<FdInv_ExchangeLog>();
        }
    
        public string InvNo { get; set; }
        public string RmNo { get; set; }
        public string BookDate { get; set; }
        public string BookTime { get; set; }
        public string InDate { get; set; }
        public string InTime { get; set; }
        public short InNumbers { get; set; }
        public string MemberNo { get; set; }
        public string MemberName { get; set; }
        public string OpenUserId { get; set; }
        public string OpenUserName { get; set; }
        public string AccUserId { get; set; }
        public string AccUserName { get; set; }
        public string AccDate { get; set; }
        public string AccTime { get; set; }
        public string CustName { get; set; }
        public string OrderUserId { get; set; }
        public string OrderUserName { get; set; }
        public int DiscRate { get; set; }
        public string OutDate { get; set; }
        public string OutTime { get; set; }
        public string CloseUserId { get; set; }
        public string CloseUserName { get; set; }
        public string Rem { get; set; }
        public int FdCost { get; set; }
        public int RmCost { get; set; }
        public Nullable<int> ZD { get; set; }
        public Nullable<int> BeerZD { get; set; }
        public Nullable<int> BeerCash { get; set; }
        public int Serv { get; set; }
        public int Disc { get; set; }
        public Nullable<int> Tax { get; set; }
        public int Tot { get; set; }
        public int Cash { get; set; }
        public Nullable<int> Cash_Targ { get; set; }
        public int Vesa { get; set; }
        public string VesaName { get; set; }
        public Nullable<int> Vesa_Targ { get; set; }
        public string VesaName_Targ { get; set; }
        public int GZ { get; set; }
        public int AccOkZD { get; set; }
        public int MembCard { get; set; }
        public int NoPayed { get; set; }
        public string WorkDate { get; set; }
        public bool Void { get; set; }
        public string VoidUserId { get; set; }
        public string VoidUserName { get; set; }
        public bool GZOk { get; set; }
        public string GZOkDate { get; set; }
        public string GZOkTime { get; set; }
        public int MorePayed { get; set; }
        public bool Booked { get; set; }
        public string CardConsumeMNo { get; set; }
        public string CardConsumeMName { get; set; }
        public string VesaNo { get; set; }
        public string VesaNo_Targ { get; set; }
        public string GZName { get; set; }
        public int FixedDisc { get; set; }
        public System.Guid msrepl_tran_version { get; set; }
    
        public virtual ICollection<FdCashBak> FdCashBak { get; set; }
        public virtual ICollection<FdInv_ExchangeLog> FdInv_ExchangeLog { get; set; }
    }
}
