﻿using ComponentApplicationServiceInterface.Context.Response;
using Pos.Model.DbFood.Context;
using Pos.Model.DbFood.RespModel;
using System;
using System.Collections.Generic;
using Pos.Drive.DbFood.ProductCalculation;
using System.Linq;
using System.Text;
using Pos.Common;

namespace Pos.Drive.DbFood
{
    public class FoodDrive : DriveBase
    {
        public ResponseContext<List<GetStoreReportModel>> GetStoreReport(GetStoreReportContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    if (context.StartTime <= DateTime.MinValue || context.EndTime <= DateTime.MinValue)
                        throw new ExMessage("时间段金额汇总为必填项！");
                    if (context.StartTime.Day + 1 == context.EndTime.Day)
                        throw new ExMessage("时间间隔不可超过一个月！");

                    var data = app.Food.GetStoreReport(context);

                    var returnData = new UnifiedComputingSystem().UnifiedCompute(data, new List<string>() { "Type" });

                    return returnData.OrderBy(w => w.FtNo).ToList();
                }
                catch (Exception ex)
                {
                    ex.HandlerException();
                    throw ex;
                }
            });
        }

        public ResponseContext<List<GetStoreFtTypeReportModel>> GetTypeDetailReport(GetStoreFtTypeReportContext context)
        {
            return ActionFun.Run(context, () =>
            {
                return app.Food.GetTypeDetailReport(context);
            });
        }

        public ResponseContext<List<GetHeadCountModel>> GetHeadCountReport(GetStoreReportContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    if (context.StartTime <= DateTime.MinValue || context.EndTime <= DateTime.MinValue)
                        throw new ExMessage("时间段金额汇总为必填项！");
                    if (context.StartTime.Day + 1 == context.EndTime.Day)
                        throw new ExMessage("时间间隔不可超过一个月！");

                    var data = app.Food.GetHeadCountReport(context);
                    data.ForEach(x =>
                    {
                        x.Category1 = x.Category1.ToLower();
                        x.Type = x.Type.ToLower();
                    });
                    int index = 0;
                    var returnData = data.GroupBy(w => w.Category1).Select(w =>
                    {
                        string business = "房费+餐";
                        if (!string.IsNullOrEmpty(w.Key))
                        {
                            if (w.Key.Contains("自助餐"))
                                business = "K+自助餐";
                            if (w.Key.Contains("纯自助餐"))
                                business = "房费+餐";
                            if (w.Key.Contains("团购"))
                                business = "团购";
                            if (w.Key.Contains("银行活动"))
                                business = "银行合作";
                            if (w.Key.Contains("赠送类"))
                                business = "赠送类";
                        }

                        var detail = w.GroupBy(x => x.Type).Select(x =>
                        {
                            index++;
                            var categorys = new List<string>();
                            if (!string.IsNullOrEmpty(x.Key))
                                categorys = x.Key.Split('-').ToList();

                            return new HeadCountDetail()
                            {
                                Index = index,
                                Category1 = categorys.Count > 0 ? categorys[0] : "",
                                Category2 = categorys.Count > 1 ? categorys[1] : "",
                                PeoCount = x.Sum(z => z.PeoNumber)
                            };
                        }).ToList();

                        return new GetHeadCountModel()
                        {
                            Details = detail,
                            Business = business
                        };
                    }).ToList();

                    return returnData;
                }
                catch (Exception ex)
                {
                    ex.HandlerException();
                    throw ex;
                }
            });
        }

        public ResponseContext<List<WaitCreateModel>> GetWaitCreate(WaitCreateContext context)
        {
            return ActionFun.Run(context, () =>
            {
                return app.Food.GetWaitCreate();
            });
        }

        public ResponseContext<List<GetFoodInfoModel>> GetFoodInfo(GetFoodInfoContext context)
        {
            return ActionFun.Run(context, () =>
            {
                return app.Food.IQueryable(w => context.FdNos.Contains(w.FdNo)).Select(w => new GetFoodInfoModel()
                {
                    FdNo = w.FdNo,
                    FdCName = w.FdCName
                }).ToList();
            });
        }
    }
}
