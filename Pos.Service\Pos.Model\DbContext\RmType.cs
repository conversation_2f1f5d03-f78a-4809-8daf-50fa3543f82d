//------------------------------------------------------------------------------
// <auto-generated>
//    此代码是根据模板生成的。
//
//    手动更改此文件可能会导致应用程序中发生异常行为。
//    如果重新生成代码，则将覆盖对此文件的手动更改。
// </auto-generated>
//------------------------------------------------------------------------------

namespace Pos.Model.DbContext
{
    using System;
    using System.Collections.Generic;
    
    public partial class RmType
    {
        public RmType()
        {
            this.Room = new HashSet<Room>();
            this.RtAuto = new HashSet<RtAuto>();
            this.RtAutoZD = new HashSet<RtAutoZD>();
        }
    
        public string RtNo { get; set; }
        public string RtName { get; set; }
        public short MaxP { get; set; }
        public bool NoServ { get; set; }
        public string AccType { get; set; }
        public int RmPrice { get; set; }
        public Nullable<int> SRmPrice { get; set; }
        public Nullable<int> WeekEndPrice { get; set; }
        public bool RealRoom { get; set; }
        public bool CanAutoZD { get; set; }
        public int MaxZDRate { get; set; }
        public string RmCostType { get; set; }
        public int ServRate { get; set; }
        public int RmPrice_Person { get; set; }
        public int SRmPrice_Person { get; set; }
        public int WeekEndPrice_Person { get; set; }
        public int RmPrice_PerUnit { get; set; }
        public int SRmPrice_PerUnit { get; set; }
        public int WeekEndPrice_PerUnit { get; set; }
        public int UnitMinutes { get; set; }
        public int MinMinutesOfTimeZone { get; set; }
        public int MinMinutesOfTimeUnit { get; set; }
        public bool SetClearing { get; set; }
        public System.Guid rowguid { get; set; }
        public System.Guid msrepl_tran_version { get; set; }
    
        public virtual ICollection<Room> Room { get; set; }
        public virtual ICollection<RtAuto> RtAuto { get; set; }
        public virtual ICollection<RtAutoZD> RtAutoZD { get; set; }
    }
}
